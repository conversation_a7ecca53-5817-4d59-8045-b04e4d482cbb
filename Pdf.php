<?php

// import for fetching the login token for passing to the template
require_once __DIR__ . '/PrintoutCurl.php';

class Pdf
{
    // make sure that those 2 variables end with a slash
    public static string $localBaseUrl = "http://localhost/api-routes/";
    public static string $apiUrl = 'https://api.baubuddy.de/int/index.php/';

    public static string $principal = "default_test";
    public static string $loginPnr = "365";
    public static string $loginPassword = "1";

    /**
     * @var string Take care to not use spaces inside this.
     */
    public static string $outputFile = "output.pdf";

    /**
     * @var bool if true, do not run wkhtmltopdf and just open the body URL in the browser.
     *  Note that this only works for macOS since Windows is untested.
     */
    public static bool $justOpenInBrowser = false;

    /**
     * @var bool if true, do not call POST login since some API URLs work without auth where
     * we don't have login credentials.
     */
    public static bool $skipLogin = false;

    public static bool $landscapeMode = false;

    public static bool $previewPdf = true;

    /**
     * @var array of arguments passed to wkhtmltopdf
     */
    /**
     * @var array<string>
     */
    public static array $additionalArguments = [];

    /**
     * Note that the value is ignored for Windows since it seems to work there.
     */
    public static bool $appendXDebugCookies = true;

    private static function onWindows(): bool
    {
        return strcasecmp(substr(PHP_OS, 0, 3), 'WIN') == 0;
    }

    /**
     * @param string $str
     * @return string
     */
    private static function escapeCharactersForTerminal($str)
    {
        if (self::onWindows()) {
            $str = str_replace('&', '^&', $str);
        }
        // do not modify the input string for macOS
        return $str;
    }

    /**
     * Compile the PDF in the directory via wkhtmltopdf.
     *
     * @param string $dir Always pass __DIR__ here
     * @param array $args parameters passed to the api-printout route
     */
    /**
     * @param string $dir
     * @param array<string> $args
     */
    public static function compile(string $dir, array $args): void
    {
        if (self::$skipLogin) {
            // set an empty one since some API endpoints
            $access_token = '';
        } else {
            $curl = new PrintoutCurl(self::$apiUrl);
            $access_token = $curl->getLoginToken(self::$loginPnr, self::$loginPassword, self::$principal);
        }

        $baseName = basename($dir);
        $api_url_body = self::$localBaseUrl . $baseName . "/template.content.php?";

        $params = [
            "apiUrl" => self::$apiUrl,
            "principal" => self::$principal,
            "authorization" => 'Bearer ' . $access_token,
        ];
        $params = array_merge($params, $args);
        $api_url_body .= urldecode(http_build_query($params));

        // escape space as urlencode() would, so the link stays intact for printing and clicking on it
        $api_url_body = str_replace(" ", "+", $api_url_body);
        echo "$api_url_body\n";

        $args = ["wkhtmltopdf"];
        $args = array_merge($args, self::$additionalArguments);

        if (self::$landscapeMode) {
            $args[] = "-O";
            $args[] = "landscape";
        }

        if (PHP_OS == "Darwin") {
            $args[] = "'" . self::escapeCharactersForTerminal($api_url_body) . "'";
        } else {
            $args[] = self::escapeCharactersForTerminal($api_url_body);
        }

        $header = $dir . '/template.header.php';
        if (file_exists($header)) {
            $args[] = '--header-html';
            if (PHP_OS == "Darwin") {
                $args[] = "'" . self::$localBaseUrl . $baseName . "/template.header.php'";
            } else {
                $args[] = self::$localBaseUrl . $baseName . "/template.header.php";
            }
        }

        $footer = $dir . '/template.footer.php';
        if (file_exists($footer)) {
            $args[] = '--footer-html';
            if (PHP_OS == "Darwin") {
                $args[] = "'" . self::$localBaseUrl . $baseName . "/template.footer.php" . "'";
            } else {
                $args[] = self::$localBaseUrl . $baseName . "/template.footer.php";
            }
        }

        if (!self::onWindows() && self::$appendXDebugCookies) {
            $args[] = "--cookie";
            $args[] = "XDEBUG_SESSION";
            $args[] = "'PHPSTORM; path=/'";
        }

        $args[] = self::$outputFile;

        echo "\n";
        foreach ($args as $arg) {
            echo "$arg ";
        }
        echo "\n\n";

        if (self::$justOpenInBrowser && PHP_OS === "Darwin") {
            shell_exec("open '" . $api_url_body . "'");

        } else {
            $output = null;
            $statusCode = null;
            exec(implode(" ", $args), $output, $statusCode);

            if ($statusCode === 0 && self::$previewPdf) {
                if (PHP_OS === "Darwin") {
                    exec("open " . self::$outputFile);

                } else if (self::onWindows()) {
                    exec("start " . $dir . "\\" . self::$outputFile);
                }
            }
        }
    }
}
