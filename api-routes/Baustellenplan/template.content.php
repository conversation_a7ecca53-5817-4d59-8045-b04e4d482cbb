<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Baustellenplan())->getData($_GET['fromDate'], $_GET['toDate'], $_GET['employeeList']);
}

function getEmployeeDisplayName($pnr, $data): string
{
    $pnr = (string)$pnr;
    foreach ($data['employees'] as $employee) {
        if ($employee['pnr'] == $pnr) {
            return $employee['displayName'] . ' (' . $pnr . ')';
        }
    }
    die('No employee found from GET v3/employees: ' . $pnr);
}

function sortProjects($projects): array
{
    usort($projects, static function ($a, $b) {
        return strnatcmp($a['projectName'], $b['projectName']);
    });
    return $projects;
}

?>

<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Baustellenplan</title>
</head>
<body>

<?php
$firstArbeitseinteilung = true;
foreach ($data['projectsGroupedByEmployee'] as $pnr => $employee) {
    if ($firstArbeitseinteilung) {
        $firstArbeitseinteilung = false;
    } else {
        ?>
        <div class="new-page"></div>
        <?php
    }
    ?>
    <!-- for https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/130 -->
    <h1 style="display: none"><?= count($data['businessWeeks']) ?></h1>
    <table class="title">
        <tr>
            <td>
                <b>Arbeitseinteilung <?= ' (' . $data['from'] . ' - ' . $data['to'] . ')'; ?></b>
                <br>
                <?= $employee['displayName']; ?>
            </td>
            <td>
                <img src="<?= $data['companyLogoUrl']; ?>" alt="logo" class="companyLogo">
            </td>
        </tr>
    </table>

    <?php
    $abwicklungsbauleiter = [];
    $vorarbeiter = [];
    $bauleiter = [];
    $projektleiter = [];
    $ungrouped = [];
    $toSort = [];

    $appendedProjects = [];
    $projectsAtBottom = [];

    // ignore Projektleiter group for now, it's just for debugging
    // the looping below is necessary to have a stable project sort without duplicates
    foreach ($employee['projects'] as $project) {
        foreach ($project['employeeGroups']['Vorarbeiter'] as $p) {
            if (!in_array($project, $appendedProjects)) {
                $vorarbeiter[$p][] = $project;
                $appendedProjects[] = $project;
            }
        }
    }

    foreach ($employee['projects'] as $project) {
        foreach ($project['employeeGroups']['Abwicklungsbauleiter'] as $p) {
            if (!in_array($project, $appendedProjects)) {
                $abwicklungsbauleiter[$p][] = $project;
                $appendedProjects[] = $project;
            }
        }
    }

    foreach ($employee['projects'] as $project) {
        foreach ($project['employeeGroups']['Bauleiter'] as $p) {
            if (!in_array($project, $appendedProjects)) {
                $bauleiter[$p][] = $project;
                $appendedProjects[] = $project;
            }
        }
    }

    foreach ($employee['projects'] as $project) {
        if (empty($project['employeeGroups']['Abwicklungsbauleiter']) &&
            empty($project['employeeGroups']['Vorarbeiter']) &&
            empty($project['employeeGroups']['Bauleiter'])) {

            if (!in_array($project, $appendedProjects)) {
                $projectsAtBottom[] = $project;
                $appendedProjects[] = $project;
            }
        }
    }

    // for https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/130
    $firstHeader = true;

    foreach ($bauleiter as $pnr2 => $projects) {
        echoTableForHeader(getEmployeeDisplayName($pnr2, $data) . ' Bauleiter',
            sortProjects($projects), $data, $firstHeader);
        $firstHeader = false;
    }

    foreach ($abwicklungsbauleiter as $pnr2 => $projects) {
        echoTableForHeader(getEmployeeDisplayName($pnr2, $data) . ' Abwicklungsbauleiter',
            sortProjects($projects), $data, $firstHeader);
        $firstHeader = false;
    }

    foreach ($vorarbeiter as $pnr2 => $projects) {
        echoTableForHeader(getEmployeeDisplayName($pnr2, $data) . ' Vorarbeiter',
            sortProjects($projects), $data, $firstHeader);
        $firstHeader = false;
    }

    echoTableForHeader('Ungruppiert', sortProjects($projectsAtBottom), $data, $firstHeader);
}

function echoTableForHeader($header, $projects, $data, $firstHeader)
{
    if (count($projects) === 0) {
        return;
    }
    ?>

    <div class="wrapper">
        <?php
        // todo revert for https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/130
        /*        if (true || $firstHeader) { */ ?><!--
            <div class="projectManager"><?php /*= $header */ ?></div>
        <?php /*} else { */ ?>
            <div class="projectManager" style="page-break-before: always"><?php /*= $header */ ?></div>
        --><?php /*} */ ?>
        <div class="projectManager"><?= $header ?></div>

        <table class="projectsTable">
            <tr>
                <td class="bigFont" style="min-width: 300px; text-align: center">
                    <b>Baustelle</b>
                </td>
                <?php foreach ($data['businessWeeks'] as $weekRange) { ?>
                    <td class="bigFont" style="min-width: 180px; text-align: center">
                        <!--add year and week number in the first column header . Week number is resolved by getting the week number by the start of the range-->
                        <b><?= date("Y", strtotime($weekRange['start'])) . '-' . date("W", strtotime($weekRange['start'])); ?></b>
                    </td>
                <?php } ?>
            </tr>
            <tr>
                <td class="bigFont"></td>
                <?php foreach ($data['businessWeeks'] as $weekRange) { ?>
                    <td class="weekRange" style="text-align: center">
                        <!--add week's ranges in the second header column-->
                        <?= '(' . $weekRange['start'] . ' - ' . $weekRange['end'] . ')'; ?>
                    </td>
                <?php } ?>
            </tr>

            <?php foreach ($projects as $project) {
                echoProjectIntoTable($project, $data);
            } ?>
        </table>

    </div>
<?php }


function getConstructionStageInfo($project): string
{
    $constructionStages = $project['constructionStages'];
    if (count($constructionStages) >= 1) {
        $parts = [];
        foreach ($constructionStages as $cs) {
            try {
                $startDate = (new DateTime($cs['start_date']))->format('d.m.Y');
                $endDate = (new DateTime($cs['end_date']))->format('d.m.Y');
            } catch (Exception $e) {
                die($e);
            }
            $duration = $cs['duration'] ? ' ' . $cs['duration'] . ' m²' : '';
            $part = $cs['name'] . ' (' . $startDate . '-' . $endDate . ')' . $duration;
            if (!in_array($part, $parts)) {
                $parts[] = $part;
            }
        }
        if (count($parts) >= 1) {
            return '<br>' . implode('<br>', $parts);
        }
    }
    return '';
}

function teamToKey($team): string
{
    $members = [];
    foreach ($team['teamMembers'] as $member) {
        $members[] = $member['id'];
    }
    // sort so multiple teams with employees out of order are not displayed multiple times
    sort($members);
    return $team['teamName'] . implode('|', $members);
}

/**
 * @param string $yearWeek @param string $weekNumber a string like 2023-23
 * @return array an array with 5 elements in format: 2023-06-05
 */
function getMondayToFridayForWeekNumber(string $yearWeek): array
{
    list ($year, $week) = explode('-', $yearWeek);
    $date = new DateTime ();
    $date->setISODate(intval($year), intval($week));
    $result = [$date->format('Y-m-d')];
    $date->modify('+1 day');
    $result[] = $date->format('Y-m-d');
    $date->modify('+1 day');
    $result[] = $date->format('Y-m-d');
    $date->modify('+1 day');
    $result[] = $date->format('Y-m-d');
    $date->modify('+1 day');
    $result[] = $date->format('Y-m-d');
    return $result;
}

/**
 * @param string $date a date in format yyyy-mm-dd
 * @return string a German 2 char weekday like Mo
 */
function getWeekday(string $date): string
{
    $weekday_num = (int)date('w', strtotime($date));
    $weekday_map = ['Mo', 'Di', 'Mi', 'Do', 'Fr'];
    return $weekday_map[$weekday_num - 1];
}

/**
 * @param string $yearWeek a string like 2023-23
 * @return string the worked days in the week (Saturday and Sunday is not counted) or an empty string
 */
function checkIfEmployeeWorkedAllDays(int $employeeNo, array $project, string $yearWeek): string
{
    $days = getMondayToFridayForWeekNumber($yearWeek);
    $totalDays = [];

    foreach ($project['workingOrders'] as $wo) {
        if (in_array($wo['startDate'], $days)) {
            foreach ($wo['teams']['preplanned'] ?? [] as $preplannedTeams) {
                // note that humanResources can be empty which probably means that there is a team
                // with no members
                foreach ($preplannedTeams['humanResources'] ?? [] as $employee) {
                    if ($employee['employeeNo'] === $employeeNo) {
                        $totalDays[] = $wo['startDate'];
                    }
                }
            }
        }
    }

    // strip out cases where an employee works across working orders
    $totalDays = array_unique($totalDays);
    if (count($totalDays) === 5) {
        return '';
    }
    sort($totalDays);

    $result = [];
    foreach ($totalDays as $day) {
        $result[] = getWeekday($day);
    }

    return ' (' . implode(', ', $result) . ')';
}

function echoProjectIntoTable($project, $data)
{
    echo '<tr>';

    $date = '';
    if ($project['projectValidStartDate'] && $project['projectValidEndDate']) {
        $date = date('d.m.Y', strtotime($project['projectValidStartDate'])) . '-' .
            date('d.m.Y', strtotime($project['projectValidEndDate']));

    } else if ($project['projectValidStartDate']) {
        $date = date('d.m.Y', strtotime($project['projectValidStartDate']));
    }
    if ($date) {
        $date .= '<br>';
    }

    $first = $project['externalProjectNo'] ? $project['externalProjectNo'] . '<br>' : '';
    $constructionStageInfo = getConstructionStageInfo($project);
    $detail = $project['detailDescription'] ? '<br>' . $project['detailDescription'] : '';

    $kpis = implode('<br>', $project['kpis']);
    if ($kpis != '') {
        $kpis .= '<br>';
    }

    $labels = '';
    foreach ($project['labels'] as $label) {
        $name = $label['name'];
        $color = $label['color'];
        $textColor = $label['textColor'];
        $labels .= "<div class='project-label' style='background-color: $color; color: $textColor'>$name</div>";
    }
    if ($labels != '') {
        $labels = '<div class="div-project-labels">' . $labels . '</div>';
    }

    echo "<td class='projectInfo'>$first
{$project['projectName']}
<br>$kpis $labels $date $detail $constructionStageInfo
</td>";

    // loop through the week ranges and working orders to check in which week range
    // the working order fits
    foreach ($data['businessWeeks'] as $weekRange) { ?>
        <td class="team">
            <?php
            $weekNo = DateTime::createFromFormat('d.m.Y', $weekRange['start'])->format('Y-W');

            foreach ($data['constructionStages'][$project['projectNo']] ?? [] as $cs) {
                if ($cs['weekNumber'] === $weekNo) {
                    echo '<div class="greenLine"></div>';
                    if (isset($data['weeklyConstructionStageComments'][$cs['id']][$weekNo])) {
                        echo $data['weeklyConstructionStageComments'][$cs['id']][$weekNo]['commentText'] . '<br>';
                    }
                    break;
                }
            }

            foreach ($project['filteredEmployees'] as $weekNumber => $employees) {
                // check if team date is in the date range of the business week to add team members
                if ($weekNumber === date('Y-W', strtotime($weekRange['start']))) {
                    // remember added teams to not have duplicate teams from different working orders
                    $addedTeams = [];
                    foreach ($employees as $employeeData) {
                        $employeeData = array_unique($employeeData, SORT_REGULAR);
                        foreach ($employeeData as $e) {
                            $key = teamToKey($e);
                            if (in_array($key, $addedTeams)) {
                                continue;
                            }
                            if (count($addedTeams) >= 1) {
                                echo '<div style="height: 15px"></div>';
                            }
                            $addedTeams[] = $key;
                            $teamSize = $e['teamSize'] ? ' (' . $e['teamSize'] . ')' : "";
                            echo '<b>' . $e['teamName'] . $teamSize . '</b>';
                            if (count($e['teamMembers']) >= 1) {
                                echo '<ul>';
                                foreach ($e['teamMembers'] as $member) {
                                    echo '<li>' .
                                        $member['name'] .
                                        checkIfEmployeeWorkedAllDays($member['id'], $project, $weekNumber) .
                                        '</li>';
                                }
                                echo '</ul>';
                            }
                        }
                    }
                }
            }
            ?>
        </td>

    <?php }
    echo '</tr>';
}

?>

</body>
</html>

<!--suppress CssUnusedSymbol -->
<style>

    html, body {
        margin: 0;
        padding: 0;
    }

    .project-label {
        display: inline-block;
        padding: 4px;
        margin: 1px;
        border-radius: 24%;
        line-height: 1;
    }

    ul {
        margin: 0;
        padding-left: 15px;
    }

    .new-page {
        page-break-before: always;
    }

    html {
        font-family: sans-serif;
    }

    .title {
        width: 100%;
        font-size: xx-large;
    }

    .projectManager {
        margin-top: 30px;
        margin-bottom: 6px;
        font-size: xx-large;
    }

    .projectsTable {
        width: 100%;
        border-collapse: collapse;
    }

    .projectsTable td {
        border: 1px solid black;
        vertical-align: top;
    }

    .projectsTable th {
        border: 1px solid black;
    }

    .bigFont {
        background: rgb(204, 204, 204);
        font-size: larger;
    }

    .weekRange {
        background: rgb(204, 204, 204);
        font-size: x-small;
    }

    .projectInfo {
        background: rgb(228, 228, 228);
        font-size: medium;
        padding: 6px;
        width: 50px;
    }

    .team {
        background: white;
        font-size: small;
        width: 20px !important;
    }

    .employee {
        font-size: 12px;
        text-align: left;
    }

    .greenLine {
        width: 100%;
        height: 6px;
        background-color: rgb(102, 199, 76);
    }

</style>
