<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Baustellenplan
{
    private function downloadProjektLeiterProjects(string|int $employeeNo, string $baseUrl, PrintoutCurl $curl): array
    {
        $json = $curl->_simple_call('get',
            sprintf("$baseUrl/v3/team/joined/%s?role=Projektleiter", $employeeNo),
            [], PrintoutHelper::getHeadersForApiCalls());
        return array_unique(json_decode($json, true)['projects'] ?? []);
    }

    private function downloadGruppenLeiterProjects(
        string|int $employeeNo, array $projektLeiterProjectNos, string $baseUrl, PrintoutCurl $curl): array
    {
        $json = $curl->_simple_call('get',
            sprintf("$baseUrl/v3/team/joined/%s?role=Gruppenleiter", $employeeNo),
            [], PrintoutHelper::getHeadersForApiCalls());
        $projects = json_decode($json, true)['projects'] ?? [];

        // hold projects which are in Gruppenleiter, but not in Projektleiter
        $result = [];
        foreach ($projects as $glProject) {
            if (!in_array($glProject, $projektLeiterProjectNos)) {
                $result[] = $glProject;
            }
        }
        return array_unique($result);
    }

    private function downloadProjects(array $projectNos, string $fromDate, string $toDate, PrintoutCurl $curl): array
    {
        $partial = 'projectNo,externalProjectNo,projectName,projectValidStartDate,projectValidEndDate,' .
            'detailDescription,labelIds';

        $projects = [];
        // chunk to not blow URL size limit
        $projectChunks = array_chunk($projectNos, 120);
        foreach ($projectChunks as $projectChunk) {
            // fetch all projects, which are relevant for the employees given in $employeeList
            $projectJSON = $curl->_simple_call('get',
                PrintoutHelper::getApiBaseUrl() . "/v3/projects?partial=" . $partial .
                "&filter[projectNo][in]=" . implode(',', $projectChunk) .
                "&filter[parentId][eq]=null" .
                "&filter[projectValidEndDate][gte]=" . $fromDate .
                "&filter[projectValidStartDate][lte]=" . $toDate .
                '&expand=internalParticipants',
                [], PrintoutHelper::getHeadersForApiCalls());
            $projects = array_merge($projects, json_decode($projectJSON, true) ?? []);
        }
        return $projects;
    }

    function sortByCreatedOn($a, $b): int
    {
        return -strcmp($a['createdOn'], $b['createdOn']);
    }

    public function getData($fromDate, $toDate, $employeeList): array
    {
        $templateData = [];
        $templateData['weeks'] = $this->getWeekNumbers($fromDate, $toDate);
        $templateData['from'] = date("d.m.Y", strtotime($fromDate));
        $templateData['to'] = date("d.m.Y", strtotime($toDate));

        $businessWeeks = $this->getWeekRanges($fromDate, $toDate);

        array_walk_recursive($businessWeeks, function (&$item) {
            $item = date('d.m.Y', $item);
        });

        $templateData['businessWeeks'] = $businessWeeks;

        $curl = new PrintoutCurl();
        $base = PrintoutHelper::getApiBaseUrl();
        $templateData['companyLogoUrl'] = PrintoutHelper::downloadSettings($curl)['logo'];

        $json = $curl->_simple_call('get', "$base/v1/labels",
            [], PrintoutHelper::getHeadersForApiCalls());
        $labels = json_decode($json, true);

        $employeeNos = explode(',', $employeeList);
        $projectNosByEmployeeNo = [];
        $projectsGroupedByEmployees = [];
        $projectNos2Employee = [];

        // see https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/531
        // store all Projektleiter projects and all projects where Projektleiter is unset, but where the employee
        // is Gruppenleiter
        $projects = [];

        foreach ($employeeNos as $employeeNo) {
            $projectsGroupedByEmployees[$employeeNo] =
                PrintoutHelper::downloadEmployee((int)$employeeNo, $curl);
            $projectsGroupedByEmployees[$employeeNo]['projects'] = [];

            $projektLeiterProjectNos = $this->downloadProjektLeiterProjects($employeeNo, $base, $curl);
            $projectLeiterProjects = $this->downloadProjects($projektLeiterProjectNos, $fromDate, $toDate, $curl);

            $gruppenLeiterProjectNos = $this->downloadGruppenLeiterProjects(
                $employeeNo, $projektLeiterProjectNos, $base, $curl);
            $gruppenLeiterProjects = $this->downloadProjects($gruppenLeiterProjectNos, $fromDate, $toDate, $curl);

            // store only projects which do not have a Projektleiter
            $clampedGruppenLeiterProjects = [];
            foreach ($gruppenLeiterProjects as $project) {
                $hasProjektLeiter = false;
                $internalParticipants = empty($project['internalParticipants']) ? [] : $project['internalParticipants'];
                foreach ($internalParticipants as $participant) {
                    if ($participant['role'] === 'Projektleiter') {
                        $hasProjektLeiter = true;
                        break;
                    }
                }
                if (!$hasProjektLeiter) {
                    $clampedGruppenLeiterProjects[] = $project;
                }
            }

            $mergedProjects = array_merge($projectLeiterProjects, $clampedGruppenLeiterProjects);
            $projectNosByEmployeeNo[$employeeNo] = [];
            foreach ($mergedProjects as $project) {
                $projectNosByEmployeeNo[$employeeNo][] = $project['projectNo'];
            }
            $projects = array_merge($projects, $mergedProjects);

            // populate data-structure to quick look-up all employees to a single project
            if (count($projectNos2Employee) === 0) {
                $projectNos2Employee = array_fill_keys($projectNosByEmployeeNo[$employeeNo], [$employeeNo]);
            } else {
                foreach ($projectNosByEmployeeNo[$employeeNo] as $projectNoForThisEmployee) {
                    if (isset($projectNos2Employee[$projectNoForThisEmployee])) {
                        $projectNos2Employee[$projectNoForThisEmployee] = array_merge(
                            $projectNos2Employee[$projectNoForThisEmployee], [$employeeNo]);

                    } else {
                        $projectNos2Employee[$projectNoForThisEmployee] = [$employeeNo];
                    }
                }
            }
        }

        $kpiJSON = $curl->_simple_call('get',
            "$base/keyperformanceindicators?fields=enum,target,measuringUnit,projectNo,actual",
            [], PrintoutHelper::getHeadersForApiCalls());
        $templateData['kpi'] = json_decode($kpiJSON, true);

        foreach ($projects as $key => $project) {
            $projects[$key]['labels'] = [];

            foreach ($project['labelIds'] as $labelId) {
                foreach ($labels as $label) {
                    // note that this does not check label children
                    if ($labelId === $label['id']) {
                        $projects[$key]['labels'][] = [
                            'name' => $label['displayText'],
                            'color' => $label['colorCode'],
                            'textColor' => PrintoutHelper::isDarkColor($label['colorCode']) ? 'white' : 'black'
                        ];
                        break;
                    }
                }
            }

            $projects[$key]['kpis'] = [];

            foreach ($templateData['kpi'] as $kpi) {
                if ($kpi['projectNo'] === $project['projectNo']) {
                    // note that there can be multiple KPIs to a single project
                    $projects[$key]['kpis'][] = $kpi['actual'] . '/' . $kpi['target'] . ' ' . $kpi['measuringUnit'];
                    break;
                }
            }

            if (!isset($project['employeeGroups'])) {
                // add empty array for stable sorting
                $projects[$key]['employeeGroups']['Projektleiter'] = [];
                $projects[$key]['employeeGroups']['Bauleiter'] = [];
                $projects[$key]['employeeGroups']['Abwicklungsbauleiter'] = [];
                $projects[$key]['employeeGroups']['Vorarbeiter'] = [];
                $projects[$key]['employeeGroups']['Ungrouped'] = [];
            }

            $internalParticipants = empty($project['internalParticipants']) ? [] : $project['internalParticipants'];
            foreach ($internalParticipants as $employee) {
                $pnr = [$employee['employeeNo']];
                switch ($employee['role']) {
                    case 'Projektleiter':
                        $projects[$key]['employeeGroups']['Projektleiter'] =
                            array_merge($projects[$key]['employeeGroups']['Projektleiter'], $pnr);
                        break;

                    case 'Bauleiter':
                        $projects[$key]['employeeGroups']['Bauleiter'] =
                            array_merge($projects[$key]['employeeGroups']['Bauleiter'], $pnr);
                        break;

                    case 'Abwicklungsbauleiter':
                        $projects[$key]['employeeGroups']['Abwicklungsbauleiter'] =
                            array_merge($projects[$key]['employeeGroups']['Abwicklungsbauleiter'], $pnr);
                        break;

                    case 'Vorarbeiter':
                        $projects[$key]['employeeGroups']['Vorarbeiter'] =
                            array_merge($projects[$key]['employeeGroups']['Vorarbeiter'], $pnr);
                        break;

                    default:
                        $projects[$key]['employeeGroups']['Ungrouped'] =
                            array_merge($projects[$key]['employeeGroups']['Ungrouped'], $pnr);
                }
            }
        }

        $teamJSON = $curl->_simple_call('get', "$base/v2/team/permanent",
            [], PrintoutHelper::getHeadersForApiCalls());
        $templateData['teams'] = json_decode($teamJSON, true);

        $templateData['constructionStages'] = [];
        $templateData['weeklyComments'] = [];

        // for each project fetch workingOrders and add them to the projects-object
        foreach ($projects as &$project) {
            $csArray = json_decode($curl->_simple_call('get',
                "$base/v1/constructionstages?projectNo=" . $project['projectNo'],
                [], PrintoutHelper::getHeadersForApiCalls()), true);

            $project['constructionStages'] = $csArray;

            foreach ($csArray as $cs) {
                if ($cs['status'] !== 'NEW') {
                    continue;
                }

                $weekNumbers = $this->getWeekNumbers($cs['start_date'], $cs['end_date']);
                $mondays = self::getMondays($weekNumbers);

                foreach ($mondays as $monday) {
                    if (in_array($monday['week'], $templateData['weeks'])) {
                        $comments = json_decode($curl->_simple_call('get',
                            "$base/v1/constructionstages/" . $cs['ID'] . '/comments?startDate=' . $monday['monday'],
                            [], PrintoutHelper::getHeadersForApiCalls()), true);

                        if ($comments) {
                            if (count($comments) >= 1) {
                                usort($comments, array($this, 'sortByCreatedOn'));
                            }

                            $templateData['weeklyConstructionStageComments'][$cs['ID']][$monday['week']] = $comments[0];
                        }
                    }
                }

                foreach ($weekNumbers as $weekNumber) {
                    $existingWeekNumbers = $templateData['constructionStages'][$project['projectNo']] ?? [];
                    if (!in_array($weekNumber, $existingWeekNumbers)) {
                        $templateData['constructionStages'][$project['projectNo']][] = [
                            'name' => $cs['name'],
                            'id' => $cs['ID'],
                            'startDate' => $cs['start_date'],
                            'endDate' => $cs['end_date'],
                            'duration' => $cs['duration'],
                            'weekNumber' => $weekNumber,
                        ];
                    }
                }
            }

            $woJSON = $curl->_simple_call('get',
                "$base/v3/workingorders?partial=workingOrderNo,projectNo,startDate&expand=teams" .
                "&filter[projectNo][in]=" . $project['projectNo'] .
                // filter out deleted working orders
                '&filter[status][ne]=5',
                [], PrintoutHelper::getHeadersForApiCalls());
            $project['workingOrders'] = json_decode($woJSON, true);

            // add filteredEmployees
            $project['filteredEmployees'] = [];
            foreach ($project['workingOrders'] as $woData) {
                foreach ($woData['teams']['preplanned'] ?? [] as $team) {

                    // note that humanResources can be empty which probably means that there is a team
                    // with no members
                    foreach ($team['humanResources'] ?? [] as $employee) {
                        $weekNumber = date('Y-W', strtotime($woData['teams']['preplanned'][0]['date']));

                        $teamMembers = [];
                        $project['workingEmployees'] = [];
                        foreach ($team['humanResources'] as $employee2) {
                            $teamMembers[] = [
                                'id' => $employee2['employeeNo'],
                                'name' => $employee2['displayName']
                            ];
                            $project['workingEmployees'][$woData['startDate']][] = $employee2['employeeNo'];
                        }

                        $employeeDataSet = [
                            'teamName' => $team['teamName'],
                            'teamSize' => $team['teamSize'],
                            'teamMembers' => $teamMembers,
                            'displayName' => $employee['displayName'],
                            'weekNumber' => $weekNumber,
                        ];
                        $project['filteredEmployees'][$weekNumber][$employee['employeeNo']][] = $employeeDataSet;
                    }
                }
            }
            // lookup to whom this project belongs
            $employeeNos = $projectNos2Employee[$project['projectNo']];
            // add project to $projectsGroupedByEmployees
            foreach ($employeeNos as $employeeNo) {
                $projectsGroupedByEmployees[$employeeNo]['projects'][$project['projectNo']] = $project;
            }
        }

        $employeeJSON = $curl->_simple_call('get', "$base/v3/employees",
            [], PrintoutHelper::getHeadersForApiCalls());
        $templateData['employees'] = json_decode($employeeJSON, true);

        $templateData['employeeList'] = $employeeNos;
        $templateData['projectsGroupedByEmployee'] = $projectsGroupedByEmployees;
        return $templateData;
    }

    /**
     * @param $start string|null in ISO8601
     * @param $end string|null in ISO8601
     * @return array an array of strings in Y-W format
     * @noinspection PhpMissingParamTypeInspection
     */
    private function getWeekNumbers($start, $end): array
    {
        if (is_null($end)) {
            return [];
        }
        $weekNumbers = [];
        $startTimestamp = strtotime($start);
        $endTimestamp = strtotime($end);

        while ($startTimestamp <= $endTimestamp) {
            $weekNumber = date('Y-W', $startTimestamp);
            if (!in_array($weekNumber, $weekNumbers)) {
                $weekNumbers[] = $weekNumber;
            }
            $startTimestamp = strtotime('+1 day', $startTimestamp);
        }

        return $weekNumbers;
    }

    private function getMondays($yearWeeks): array
    {
        $mondays = array();
        foreach ($yearWeeks as $year_week) {
            list ($year, $week) = explode('-', $year_week);
            $date = new DateTime ();
            $date->setISODate(intval($year), intval($week));
            $mondays[] = [
                'week' => $year_week,
                'monday' => $date->format('Y-m-d')
            ];
        }
        return $mondays;
    }

    protected function getWeekRanges($start, $end): array
    {
        $timeStart = strtotime($start);
        $timeEnd = strtotime($end);
        $out = [];
        $milestones[] = $timeStart;
        $timeEndWeek = strtotime('next Monday', $timeStart);
        while ($timeEndWeek < $timeEnd) {
            $milestones[] = $timeEndWeek;
            $timeEndWeek = strtotime('+1 week', $timeEndWeek);
        }
        $milestones[] = $timeEnd;
        $count = count($milestones);
        for ($i = 1; $i < $count; $i++) {
            if ($i == $count - 1) {
                $out[] = [
                    'start' => $milestones[$i - 1],
                    'end' => $milestones[$i]
                ];
            } else {
                $out[] = [
                    'start' => $milestones[$i - 1],
                    'end' => $milestones[$i] - 1
                ];
            }
        }
        return $out;
    }
}
