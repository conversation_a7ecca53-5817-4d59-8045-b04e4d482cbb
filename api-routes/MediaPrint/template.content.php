<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
/** @noinspection HtmlUnknownAttribute */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_MediaPrint())->getData($_GET['projectNo'], $_GET['imagesPerPage'], $_GET['workingOrderNo'] ?? null);
}

$imagesPerPage = $data['imagesPerPage'];
$logo = $data['logo'];
$project = $data['project'];
$workingOrder = $data['workingOrder'];

$projectDate = $project['projectValidStartDate'] ?? '';
$projectManager = $project['technicalContactKey'] ?? '';
$projectExternalNo = $project['externalProjectNo'] ?? '';
$workingOrderNumber = $workingOrder['aanr'] ?? '';
$workingOrderDescription = $workingOrder['kurz_text'] ?? '';

function printRows($data, $value): void
{
    $imagesPerPage = intval($data['imagesPerPage']);
    $description = trim($value['description']);

    echo "<table style='width: 100%'><tbody>";

    if (!empty($description)) {
        echo sprintf("<tr><td class='text-size'><b>%s: </b></td><td>%s</td></tr>", PrintoutHelper::lang('title'), $description);
    }

    if ($value['showWorkingOrderNumber']) {
        echo sprintf("<tr><td %s class='text-size'><b>%s: </b></td><td>%s</td></tr>",
            // use hardcoded pixel width so the working order text is not broken to a second row
            $imagesPerPage === 4 ? "style='width: 90px'" : "style='width: 130px'",
            PrintoutHelper::lang('working_order'), $value['rel_key']);
    }

    if ($value['showUploader']) {
        echo sprintf("<tr><td class='text-size'><b>%s: </b></td><td>%s</td></tr>",
            PrintoutHelper::lang('author'), getEmployeeName($data['employees'], $value['uploader']));
    }

    if ($value['showUploadDate']) {
        $uploadTime = date('d.m.Y H:i:s', strtotime($value['upload_time']));
        echo sprintf("<tr><td class='text-size'><b>%s: </b></td><td>%s</td></tr>", PrintoutHelper::lang('uploaded_at'), $uploadTime);
    }

    if ($value['showCreationDate']) {
        $creation_time = date('d.m.Y H:i:s', strtotime($value['creation_time']));
        echo sprintf("<tr><td class='text-size'><b>%s: </b></td><td>%s</td></tr>", PrintoutHelper::lang('created_at'), $creation_time);
    }

    echo "</tbody></table>";
    if ($imagesPerPage > 2) {
        return;
    }

    if ($value['showComments']) {
        echo getComments($data, $value['fid']);
    }
}

function getEmployeeName($employees, $pnr): string
{
    return empty($employees[intval($pnr)]['name']) ? '' : $employees[intval($pnr)]['name'];
}

function getComments($data, $fid): string
{
    $result = '';
    $commentsData = $data['comments'];
    foreach ($commentsData as $key => $comments) {
        if (empty($comments)) {
            continue;
        }
        if ($key === intval($fid)) {
            for ($i = 0; $i < count($comments); $i++) {
                $comment = $comments[$i];
                $date = PrintoutHelper::dateConverter($comment['createdOn'], 'd.m.Y H:i:s');
                $author = getEmployeeName($data['employees'], $comment['createdBy']);
                $text = $comment['commentText'];
                // add 3 px left margin for parity to the <table> above
                $result .= sprintf("<p class='text-size' style='margin: 10px 0 0 3px'><b>%s </b>(%s): %s</p>", $author, $date, $text);
            }
        }
    }
    return $result;
}

?>

<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <script src="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("assets/js/twemoji.min.js") ?>"></script>
    <script>
        window.onload = function () {
            twemoji.parse(document.body, {size: 16});
        };
    </script>
    <title>Media Print</title>
</head>
<body>
<?php foreach ($data['files'] as $files) { ?>
    <div class="page">
        <header style="display: -webkit-box">
            <table style="width: 85%;">
                <tr>
                    <td><?= PrintoutHelper::lang('customer_name') ?>:</td>
                    <td><b><?= $project['customerName'] ?></b></td>
                </tr>
                <?php if (!empty($workingOrderNumber)) { ?>
                    <tr>
                        <td><?= PrintoutHelper::lang('working_order') ?>:</td>
                        <td><b><?= $workingOrderNumber ?></b></td>
                    </tr>
                <?php } ?>
                <tr>
                    <td><?= PrintoutHelper::lang('project_description') ?>:</td>
                    <td><b><?= $project['projectName'] ?></b></td>
                </tr>
                <?php if (!empty($workingOrderDescription)) { ?>
                    <tr>
                        <td><?= PrintoutHelper::lang('working_order_description') ?>:</td>
                        <td><b><?= $workingOrderDescription ?></b></td>
                    </tr>
                <?php } ?>
                <?php if (!empty($projectDate)) { ?>
                    <tr>
                        <td><?= PrintoutHelper::lang('date') ?>:</td>
                        <td>
                            <b>
                                <?php
                                $date = DateTime::createFromFormat('Y-m-d', $projectDate);
                                if ($date) {
                                    echo $date->format('d.m.Y');
                                } else {
                                    die_with_response_code(Response::SERVER_ERROR, "Invalid date format: $projectDate");
                                }
                                ?>
                            </b>
                        </td>
                    </tr>
                <?php } ?>
            </table>
            <div class="logo" style="text-align: right">
                <?php
                if (method_exists('PrintoutsKeramikStein', 'selectCorrectLogo')) {
                    echo PrintoutsKeramikStein::selectCorrectLogo($projectExternalNo, $projectManager);
                } else { ?>
                    <img width="100%" src="<?= $logo ?>" alt="logo"/>
                <?php } ?>
            </div>
        </header>

        <?php if ($imagesPerPage <= 2) { ?>
            <!-- subtract 10mm paddings to end up at 200mm to fill whole width -->
            <div style="width: 200mm;">
                <?php foreach ($files as $file) { ?>
                    <?php
                    $class = $imagesPerPage == 1 ? 'one-img-per-page' : 'two-img-per-page';
                    ?>
                    <div class="<?= $class ?>">
                        <img src="<?= $file['thumb_path'] ?? $file['filepath'] ?>" alt=""/>
                        <div>
                            <?php printRows($data, $file); ?>
                        </div>
                    </div>
                <?php } ?>
            </div>
        <?php } else { ?>
            <div>
                <?php for ($i = 0; $i < count($files) - 1; $i += 2) { ?>
                    <!-- use less width than the full page since wkhtmltopdf can not properly lay out the children and overflows to the right otherwise -->
                    <div style="display: -webkit-box; width: 190mm">
                        <div class="four-img-per-page" style="margin-right: 10px;">
                            <?= '<img src="' . ($files[$i]['thumb_path'] ?? $files[$i]['filepath']) . '"  alt="" />' ?>
                            <div>
                                <?php printRows($data, $files[$i]); ?>
                            </div>
                        </div>
                        <div class="four-img-per-page">
                            <?= '<img src="' . ($files[$i + 1]['thumb_path'] ?? $files[$i + 1]['filepath']) . '"  alt="" />' ?>
                            <div>
                                <?php printRows($data, $files[$i + 1]); ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>

                <!-- last element if there's an odd number of files -->
                <?php if (count($files) % 2 != 0) { ?>
                    <div style="display: -webkit-box; width: 190mm">
                        <div class="four-img-per-page">
                            <?= '<img src="' . ($files[count($files) - 1]['thumb_path'] ?? $files[count($files) - 1]['filepath']) . '"  alt="" />' ?>
                            <div>
                                <?php printRows($data, $files[count($files) - 1]); ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>
        <?php } ?>
    </div>
<?php } ?>
</body>
</html>
