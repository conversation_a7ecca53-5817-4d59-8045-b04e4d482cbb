<?php
require_once __DIR__ . '/../../core/Request.php';
require_once __DIR__ . '/../../core/Response.php';
require_once __DIR__ . '/../../core/functions.php';
require_once __DIR__ . '/../../printout.helper.php';

use chillerlan\QRCode\QRCode;
use chillerlan\QRCode\QROptions;

class MediaPrintCommon
{
    /**
     * @param $projectNo string|null is null in MediaPrintV3
     * @param $imagesPerPage int only relevant for MediaPrintV1
     * @param $workingOrderNo string|int|null is null in MediaPrintV3
     * @param $schemaId string|null
     * @return array
     */
    public function getDataCommon(
        string|null     $projectNo,
        int             $imagesPerPage = 1,
        string|int|null $workingOrderNo = 0,
        string|null     $schemaId = null): array
    {
        if (!in_array($imagesPerPage, [1, 2, 3, 4])) {
            $message = "The value provided for the imagesPerPage parameter is invalid. Available values: 1, 2, 3, 4.";
            die_with_response_code(Response::BAD_REQUEST, $message);
        }

        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $data = $this->getApiData($projectNo, $workingOrderNo, $curl, $schemaId);
        $files = $data['files'];
        $settings = $data['settings'];
        $project = $data['project'];
        $workingOrder = $data['workingOrder'];

        if (count($files) == 0) {
            die_with_response_code(Response::BAD_REQUEST, "There are no files to print.");
        }
        if (count($files) > 100 && Request::isPdf() && Request::isWkhtmltopdf()) {
            die_with_response_code(Response::SERVER_ERROR,
                "The request was aborted due to large amount of files. More than 100 files are not supported. " .
                "There are " . count($files) . " files.");
        }

        if (Request::isRoute('MediaPrint') || Request::isRoute('MediaPrintV2') ||
            (!is_post_request() && Request::isRoute('MediaWorkingOrderV2'))) {

            $files = array_map(function ($file) {
                $file['showWorkingOrderNumber'] = true;
                $file['showUploader'] = true;
                $file['showUploadDate'] = true;
                $file['showCreationDate'] = true;
                $file['showComments'] = true;
                return $file;
            }, $files);
        }

        $comments = [];
        $employeeIds = [];

        foreach ($files as $file) {
            if (!in_array($file['uploader'], $employeeIds)) {
                $employeeIds[] = (int)$file['uploader'];
            }
        }
        $allFids = array_column($files, 'fid');
        $uniqueFids = array_values(array_unique(array_filter($allFids)));
        $fidToComments = [];
        $fidChunks = array_chunk($uniqueFids, 120);
        foreach ($fidChunks as $fidChunk) {
            $urls = [];
            foreach ($fidChunk as $fid) {
                $urls[] = "v1/files/single/$fid/comments";
            }
            $responses = $curl->_multi_call('get', $urls, [], PrintoutHelper::getHeadersForApiCalls());
            for ($i = 0; $i < count($responses); $i++) {
                $fid = $fidChunk[$i];
                $fidToComments[$fid] = $responses[$i]['response'];
                if (!$responses[$i]['response']) continue;
                foreach ($responses[$i]['response'] as $comment) {
                    if (!in_array($comment['createdBy'], $employeeIds)) {
                        $employeeIds[] = (int)$comment['createdBy'];
                    }
                }
            }
        }
        foreach ($files as $file) {
            $fid = $file['fid'] ?? null;
            $fileComments = $fid && isset($fidToComments[$fid]) ? $fidToComments[$fid] : [];
            $comments[$fid] = $fileComments;
        }

        $scaffoldListByRel = [];
        if (Request::isRoute('MediaPrintV2') && $projectNo !== null) {
            $scaffoldListByRel = $this->getScaffoldListByRel($files, (int)$projectNo, $curl);
        } elseif (Request::isRoute('MediaPrintV3')) {
            $scaffoldListByRel = $this->getScaffoldListByRelForV3($files, $curl);
        }

        return [
            'files' => array_chunk($files, $imagesPerPage),
            'comments' => $comments,
            'employees' => $this->downloadPartnersAndEmployees($employeeIds, $curl),
            'imagesPerPage' => $imagesPerPage,
            'logo' => $settings['logo'] ?? '',
            'project' => $project[0] ?? [],
            'workingOrder' => $workingOrder ?? [],
            'scaffoldListByRel' => $scaffoldListByRel,
        ];
    }

    /**
     * Required because the API can return uploader: -144, indicating a partner which can not be fetched via GET v3/employees.
     *
     * @return array with pnr key and array value of name and profilePictureUrl
     */
    protected function downloadPartnersAndEmployees(array $employeeIds, PrintoutCurl $curl): array
    {
        $employees = [];
        $urls = [];

        foreach ($employeeIds as $id) {
            if ($id < 0) {
                $urls[] = "v1/partners?virtualNo=" . $id;
            } else {
                $urls[] = "v3/employees/$id?showRelatedProject=false";
            }
        }

        $responses = $curl->_multi_call('get', $urls, [], PrintoutHelper::getHeadersForApiCalls());
        foreach ($responses as $index => $response) {
            PrintoutHelper::ensureStatusCode($response['statusCode'], 'GET ' . $urls[$index], $response['response']);
            $response = $response['response'];
            $id = $employeeIds[$index];

            if ($id < 0) {
                if (isset($response[0])) {
                    $partner = $response[0];
                    $firstName = $partner['firstName'] ?? null;
                    $lastName = $partner['lastName'] ?? null;
                    $employees[$id] = [
                        'name' => trim($firstName . ' ' . $lastName),
                        'profilePictureUrl' => $partner['profilePictureUrl'] ?? null
                    ];
                }
            } else {
                $employees[$id] = [
                    'name' => $response['displayName'] ?? '',
                    'profilePictureUrl' => $response['profilePicture']['url'] ?? null
                ];
            }
        }

        return $employees;
    }

    protected function getApiData(
        string|null     $projectNo,
        int|string|null $workingOrderNo,
        PrintoutCurl    $curl,
        string|null     $schemaId): array
    {
        $partial = "projectNo,projectName,projectValidStartDate,externalProjectNo,technicalContactKey,customerName";
        $urls = [
            "v1/settings/common",
            $projectNo ? "v3/projects?filter[projectNo][eq]=$projectNo&partial=$partial" : null
        ];
        $urls = array_filter($urls);

        $isPost = is_post_request();
        if ($isPost) {
            if (Request::isRoute('MediaPrintV3')) {
                $docs = Request::parse_request_body_as_json_or_die();
                if (!is_array($docs) || count($docs) === 0) {
                    die_with_response_code(Response::BAD_REQUEST, "MediaPrintV3 expects a non-empty array of documentation objects.");
                }
                $schema = PrintoutHelper::downloadSchema($schemaId, $curl);
                if ($schema['visible'] === false) {
                    die_with_response_code(Response::BAD_REQUEST, "The schema $schemaId has 'visible: false', but a visible schema is required!");
                }
                $images = $this->convertRequestBodyDocumentationsToImages($docs, $schema, $schemaId);
                // For V3, do not require projectNo or workingOrderNo, and do not fetch files by project/WO.
                $files = $this->getFiles($curl, $images);
                // Only fetch settings (and not project/WO info) if projectNo is not provided
                $responses = $curl->_multi_call('get', $urls, [], PrintoutHelper::getHeadersForApiCalls());
                return [
                    'settings' => $responses[0]['response'] ?? [],
                    'project' => [],
                    'files' => $files,
                    'workingOrder' => []
                ];
            } else {
                $images = Request::parse_request_body_as_json_or_die()['images'] ?? [];
            }
            if (count($images) == 0) {
                die_with_response_code(Response::BAD_REQUEST, "The request body did not specify any files to print!");
            }
            $files = $this->getFiles($curl, $images);
        } else {
            if ($workingOrderNo) {
                $urls[] = "v1/files/ktr-aanr/$projectNo-$workingOrderNo";
            } else if ($projectNo) {
                $urls[] = "v1/files/ktr/$projectNo";
            }
        }
        if ($workingOrderNo) {
            $urls[] = "v1/workingorders/select/$projectNo/$workingOrderNo";
        }
        $responses = $curl->_multi_call('get', $urls, [], PrintoutHelper::getHeadersForApiCalls());
        for ($i = 0; $i < count($responses); $i++) {
            $response = $responses[$i];
            PrintoutHelper::ensureStatusCode($response['statusCode'], 'GET ' . $urls[$i], $response['response']);
        }

        return [
            'settings' => $responses[0]['response'] ?? [],
            'project' => $responses[1]['response'] ?? [],
            'files' => $isPost ? $files : ($responses[2]['response'] ?? []),
            'workingOrder' => $isPost ? ($responses[2]['response'] ?? []) : ($responses[3]['response'] ?? [])
        ];
    }

    public function getFiles($curl, array $images = []): array
    {
        $result = [];
        $fileIds = array_column($images, 'fileId');
        $uniqueFileIds = array_values(array_unique($fileIds));
        $urls = array_map(fn($id) => "v1/files/single/$id", $uniqueFileIds);
        $responses = $curl->_multi_call('get', $urls, [], PrintoutHelper::getHeadersForApiCalls());

        $statusCodeFailures = 0;
        $urlCheckFailures = 0;
        $totalFiles = count($urls);

        $fileDataById = [];
        for ($i = 0; $i < count($responses); $i++) {
            $response = $responses[$i];
            $statusCode = $response['statusCode'];
            $fileId = $uniqueFileIds[$i];
            if ($statusCode !== 200) {
                $statusCodeFailures++;
                continue;
            }

            if ($response['response']) {
                $image = $response['response'];
                if (Request::isWkhtmltopdf()) {
                    $checked = false;
                    $urlValid = false;
                    if (isset($image['thumb_path'])) {
                        $checked = true;
                        $urlValid = PrintoutHelper::doesUrlReturn200($image['thumb_path']);
                    } elseif (isset($image['filepath'])) {
                        $checked = true;
                        $urlValid = PrintoutHelper::doesUrlReturn200($image['filepath']);
                    }
                    if ($checked && !$urlValid) {
                        $urlCheckFailures++;
                        continue;
                    }
                } else if (Request::isPlaywright()) {
                    if (isset($image['filepath']) && !PrintoutHelper::doesUrlReturn200($image['filepath'])) {
                        $urlCheckFailures++;
                        continue;
                    }
                }
                $fileDataById[$fileId] = $image;
            }
        }
        foreach ($images as $img) {
            $fileId = $img['fileId'];
            if (!isset($fileDataById[$fileId])) {
                continue;
            }
            $image = $fileDataById[$fileId];
            $image['description'] = $img['description'] ?? $image['description'];
            foreach (['WorkingOrderNumber', 'Uploader', 'UploadDate', 'CreationDate', 'Comments'] as $field) {
                $image["show$field"] = $this->isVisible($img, "show$field");
            }
            $result[] = $image;
        }
        if (empty($result)) {
            die_with_response_code(
                Response::BAD_REQUEST,
                "Downloading $totalFiles image file ID(s) failed: $statusCodeFailures non-200 status code(s), $urlCheckFailures invalid image URL(s)."
            );
        }
        return $result;
    }

    public function isVisible($image, $key)
    {
        return isset($image[$key]) ? filter_var($image[$key], FILTER_VALIDATE_BOOLEAN) : true;
    }

    public function convertRequestBodyDocumentationsToImages(array $docs, array $schema, $schemaId): array
    {
        if (empty($schema['children'])) {
            die_with_response_code(Response::BAD_REQUEST, "The schema $schemaId does not have any children!");
        }
        $ids = array_column($schema['children'], 'id');
        $images = [];
        foreach ($docs as $doc) {
            if (empty($doc['rel_key1'])) {
                die_with_response_code(Response::BAD_REQUEST,
                    "The request body did not specify a file ID via rel_key1 for a documentation object.");
            }
            $fileId = (int)$doc['rel_key1'];
            $meta = [];
            foreach ($doc['reportedValues'] ?? [] as $rv) {
                $meta[(int)$rv['id']] = $rv['value'];
            }
            $images[] = [
                'fileId' => $fileId,
                'description' => $meta[$ids[2]] ?? '',
                'showWorkingOrderNumber' => PrintoutHelper::isCheckboxTickedByReportedValue($meta[$ids[3]] ?? false),
                'showUploader' => PrintoutHelper::isCheckboxTickedByReportedValue($meta[$ids[4]] ?? false),
                'showComments' => PrintoutHelper::isCheckboxTickedByReportedValue($meta[$ids[5]] ?? false),
                'showCreationDate' => PrintoutHelper::isCheckboxTickedByReportedValue($meta[$ids[6]] ?? false),
                'showUploadDate' => PrintoutHelper::isCheckboxTickedByReportedValue($meta[$ids[7]] ?? false),
            ];
        }
        return $images;
    }

    /**
     * Adds preview/QR/video fields to each file for printout templates.
     * Handles isVideo, isImage, previewUrl, size, and qrString for each file.
     *
     * @param array $files Reference to the files array (chunked or flat)
     */
    protected static function processPreviewQrVideoFields(array &$files): void
    {
        $qrCodeOptions = new QROptions([
            'drawLightModules' => false,
        ]);

        foreach ($files as &$fileGroup) {
            foreach ($fileGroup as &$file) {
                $mimeType = $file['mime_type'] ?? "";
                $isVideo = str_starts_with($mimeType, "video");
                $isImage = str_starts_with($mimeType, "image");
                $file['isVideo'] = $isVideo;
                $file['isImage'] = $isImage;

                if ($isImage) {
                    $file['previewUrl'] = $file['filepath'];
                } else {
                    // use thumb path for videos or files like PDFs
                    $file['previewUrl'] = $file['thumb_path'] ?? $file['filepath'];
                }

                if ($isVideo) {
                    $url = $file['filepath'];
                    // Add file size and QR code for video files
                    $file["size"] = PrintoutHelper::probeUrlContentLength($url);
                    $file['qrString'] = (new QRCode($qrCodeOptions))->render($url);
                }
            }
        }
    }

    /**
     * For each working order from the provided files (rel_type == 'ktr-aanr'),
     * retrieves the scaffold list numbers, keyed by "<projectNo>-<workingOrderNo>" (e.g. 2502007-4).
     *
     * @param array $files Files to process
     * @param int $projectNo Main project number (used if not in file)
     * @param PrintoutCurl $curl Curl instance for API calls
     * @return array Scaffold list numbers keyed by "<projectNo>-<workingOrderNo>"
     */
    private function getScaffoldListByRel(array $files, int $projectNo, PrintoutCurl $curl): array
    {
        $workingOrderFiles = array_filter(
            $files,
            fn(array $file) => !empty($file['rel_type']) && $file['rel_type'] === 'ktr-aanr'
        );
        $workingOrderNumbers = array_unique(array_filter(array_column(
            $workingOrderFiles, 'workingOrder', 'workingOrder'), 'is_numeric'));
        if (empty($workingOrderNumbers)) {
            return [];
        }

        $partial = "workingOrderNo,scaffoldListNo";
        $workingOrderUrls = array_map(
            fn(int $workingOrderNumber) => "v3/workingorders?filter[projectNo][eq]=$projectNo&filter[workingOrderNo][eq]=$workingOrderNumber&partial=$partial",
            $workingOrderNumbers
        );

        $workingOrderResponses = $curl->_multi_call('get', $workingOrderUrls, [], PrintoutHelper::getHeadersForApiCalls());
        $scaffoldListIds = [];
        $workingOrderData = [];
        foreach ($workingOrderResponses as $index => $response) {
            $url = $workingOrderUrls[$index];
            PrintoutHelper::ensureStatusCode($response['statusCode'], "GET $url", $response['response']);
            $targetNo = $workingOrderNumbers[$index];
            $matchedWO = null;
            foreach ($response['response'] as $wo) {
                if ($wo['workingOrderNo'] === $targetNo) {
                    $matchedWO = $wo;
                    break;
                }
            }

            if ($matchedWO) {
                $workingOrderData[$targetNo] = $matchedWO;
                if (!empty($matchedWO['scaffoldListNo'])) {
                    $scaffoldListIds[] = $matchedWO['scaffoldListNo'];
                }
            }
        }
        if (empty($scaffoldListIds)) {
            return [];
        }
        $scaffoldListsById = $this->downloadScaffoldLists($projectNo, $scaffoldListIds, $curl);
        $scaffoldListByRel = [];
        foreach ($workingOrderNumbers as $woNumber) {
            $relationshipKey = "$projectNo-$woNumber";
            $scaffoldListNo = $workingOrderData[$woNumber]['scaffoldListNo'] ?? null;
            $scaffold = $scaffoldListsById[$scaffoldListNo] ?? null;
            if ($scaffold) {
                $scaffoldListByRel[$relationshipKey] = sprintf(
                    '%s-%s-%s',
                    $scaffold['rvaufnr'],
                    $scaffold['rvteilaufnr'],
                    $scaffold['rvuaufnr']
                );
            }
        }
        return $scaffoldListByRel;
    }


    private function downloadScaffoldLists(int $projectNo, array $internalNos, PrintoutCurl $curl): array
    {
        $uniqueIds = array_values(array_unique($internalNos));
        $urls = array_map(fn($no) => "v1/scaffoldlists/select/$projectNo/$no", $uniqueIds);
        $responses = $curl->_multi_call('get', $urls, [], PrintoutHelper::getHeadersForApiCalls());
        $out = [];
        foreach ($responses as $i => $resp) {
            PrintoutHelper::ensureStatusCode($resp['statusCode'], "GET $urls[$i]", $resp['response']);
            $id = $uniqueIds[$i];
            $scaffoldListData = $resp['response'];
            if (!empty($scaffoldListData)) {
                $out[$id] = $scaffoldListData[0];
            }
        }
        return $out;
    }

    /**
     * For MediaPrintV3, handles scaffold lists for files that may come from different projects.
     * Each file is processed individually based on its own project number from rel_key.
     *
     * @param array $files Files to process
     * @param PrintoutCurl $curl Curl instance for API calls
     * @return array Scaffold list numbers keyed by "<projectNo>-<workingOrderNo>"
     */
    private function getScaffoldListByRelForV3(array $files, PrintoutCurl $curl): array
    {
        $scaffoldListByRel = [];
        $filesByProjectNo = [];
        foreach ($files as $file) {
            if (($file['rel_type'] ?? '') === 'ktr-aanr') {
                $projectNo = explode('-', $file['rel_key'])[0];
                if (!isset($filesByProjectNo[$projectNo])) {
                    $filesByProjectNo[$projectNo] = [];
                }
                $filesByProjectNo[$projectNo][] = $file;
            }
        }
        foreach ($filesByProjectNo as $projectNo => $projectFiles) {
            $projectScaffoldList = $this->getScaffoldListByRel($projectFiles, (int)$projectNo, $curl);
            $scaffoldListByRel = array_merge($scaffoldListByRel, $projectScaffoldList);
        }
        return $scaffoldListByRel;
    }
}
