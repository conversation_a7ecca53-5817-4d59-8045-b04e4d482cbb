<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Mangelanzeige
{
    public function getData($schemaId, $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], true);
        $data['logo'] = PrintoutHelper::downloadSettings($curl)['logo'];
        $data['localId'] = $doc['fullDocument']['localId'];
        return $data;
    }
}