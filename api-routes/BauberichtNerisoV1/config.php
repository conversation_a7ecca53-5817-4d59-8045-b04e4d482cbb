<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "schemaId", required: true);
$config->addParameter(RouteConfigParameterType::int, "documentId", required: true);
$config->addParameter(RouteConfigParameterType::string, "displayIdType", required: false);
$config->wkhtmltopdfArguments = array_combine([
    "--margin-bottom", 
    "--margin-top"
], [
    "10mm",
    "60mm"
]);
return $config;