<?php
/** @noinspection DuplicatedCode */
/** @noinspection PhpMultipleClassDeclarationsInspection */

require_once __DIR__ . '/../../core/Request.php';
require_once __DIR__ . '/../../printout.helper.php';

enum MultiCurlType
{
    // multi curl sequence 1 has static URLs, so this enum is not used there

    // multi curl sequence 2
    case AuthorEmployee;
    case AuthorPartner;

    // only one of those 3 should be in the multi curl sequence, none is also fine
    case EntityDataResource;
    case EntityDataEmployee;
    case EntityDataProject;

    case WorkingOrderData;
    case OversizeItems;
    case AllDocumentFiles;
    case Employee;
    case Partner;
    case Supplier;
    case Task;
    case Resource;
    case Invoice;

    // multi curl sequence 2 or 3
    case Customer;

    // multi curl sequence 3
    case OversizesAll;
    case DirectoryOfServiceItems;
}

class C_KsbgChecklist
{
    private string $principal;
    private PrintoutCurl $curl;
    private string $base;

    public function __construct()
    {
        $this->principal = PrintoutHelper::getPrincipal();
        $this->curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $this->base = PrintoutHelper::getApiBaseUrl();
    }

    private function downloadProject($projectNo, $curl)
    {
        $partial = "projectName,customerNo,projectSiteAddress,projectSiteZipCode,projectSiteCity,projectValidStartDate,"
            . "customerName,parentId,projectStatus,externalProjectNo,technicalContactDisplayName,technicalContactKey";
        return PrintoutHelper::downloadProject($projectNo, $partial, $curl);
    }

    public function downloadProjectData($projectNo, array|null $firstProject): array
    {
        if ($firstProject == null) {
            $project = $this->downloadProject($projectNo, $this->curl);
        } else {
            $project = $firstProject;
        }
        $parentProjectNo = $project['parentId'];

        // if there is a parentProject we have to fetch it for the path
        if ($parentProjectNo) {
            $parentProject = $this->downloadProjectData($parentProjectNo, null);
            $project['parent'] = $parentProject;

            if ($parentProject['rootProjectName'] != $parentProject['projectName']) {
                // if the composedProjectName already exists, separate with dot
                if (isset($parentProject['composedProjectName'])) {
                    $project['composedProjectName'] =
                        $parentProject['composedProjectName'] . " · " . $parentProject['projectName'];

                } else {
                    $project['composedProjectName'] = $parentProject['projectName'];
                }
            }
            $project['rootProjectName'] = $parentProject['rootProjectName'];

        } else {
            $project['rootProjectName'] = $project['projectName'];
        }
        return $project;
    }

    private function mapSchemaData($schema, $schemaWithDescription): array
    {
        $description = "";
        if (count($schemaWithDescription) >= 1 && isset($schemaWithDescription[0]['description'])) {
            $description = $schemaWithDescription[0]['description'];
        }
        return [
            'title' => $schema['title'],
            'description' => $description,
            'positions' => $schema['children']
        ];
    }

    // from CantorTools.php > decode()
    private static function decodeIdByCantorTools($z): array
    {
        // make sure $z is positive
        if ($z < 0) {
            $z *= -1;
        }
        $t = floor((-1 + sqrt(1 + 8 * $z)) / 2);
        $x = $t * ($t + 3) / 2 - $z;
        $y = $z - $t * ($t + 1) / 2;
        return [$x, $y];
    }

    public function getData(
        string|null $schemaId,
        string|null $documentId,
        string|null $highDefinitionImages,
        string|null $photoOnSameRow,
        // set to null for KsbgChecklistOversizes which do not have those route parameters
        string|null $oversizeItemStates = null,
        string|null $staticClassAndFunctionForPostProcessing = null,
        string|null $hideEpGp = null)
    {
        $multiCurlUrls1 = [
            "$this->base/v1/documentation/schemas/$schemaId/$documentId",
            "$this->base/v1/documentation/schemas/$schemaId",
            "$this->base/v1/documentation/schemas?status=active&schemaId=$schemaId",
            "$this->base/v1/settings/common",
        ];
        $documentData = null;
        $schema = null;
        $schemaWithDescription = null;
        $logo = null;
        $multiCurlResponses1 = $this->curl->_multi_call('get', $multiCurlUrls1, [], PrintoutHelper::getHeadersForApiCalls());
        for ($i = 0; $i < count($multiCurlResponses1); $i++) {
            $statusCode = $multiCurlResponses1[$i]['statusCode'];
            $response = $multiCurlResponses1[$i]['response'];
            if ($statusCode != 200) {
                die_with_response_code(message: 'GET ' . $multiCurlUrls1[$i] . " failed with " . $statusCode . "\n" . print_r($response, true));
            }
            if ($i == 0) {
                $documentData = $response;
                PrintoutHelper::$documentChildrenForPdfMerging = $response['children'];
            } else if ($i == 1) {
                $schema = $response;
                if (!$schema['visible']) {
                    $id = $schema['id'];
                    die_with_response_code(
                        Response::BAD_REQUEST, "The schema $id has 'visible: false', but a visible schema is required!");
                }
            } else if ($i == 2) {
                $schemaWithDescription = $response;
            } else if ($i == 3) {
                $logo = $response['logo'];
            }
        }

        $documentData['children'] = PrintoutHelper::adjustTypeTimeChildrenByOffsetHeader($documentData['children'], getallheaders());
        foreach ($documentData['children'] as &$child) {
            if (isset($child ['displayInside']) && $child ['displayInside'] === $child ['parentId']) {
                unset($child['displayInside']);
            }
        }

        // Patch in parentId. The clients do not support both displayInside and parentId, but this printout expects it to be set
        foreach ($documentData['children'] as &$child) {
            if (isset($child ['displayInside']) && isset($child['parentId']) && $child['parentId'] === $documentData['id']) {
                foreach ($documentData['children'] as $child2) {
                    if ($child2['id'] === $child['displayInside']) {
                        $child['parentId'] = $child2['parentId'];
                        break;
                    }
                }
            }
        }

        $schemaData = $this->mapSchemaData($schema, $schemaWithDescription);
        $relType = $documentData['documentRelType'];

        // the URLs are all dynamically built, depending on relType and others
        $multiCurlUrls2 = [];
        // contains arrays which always have the type key and other optional keys, depending on the type
        $multiCurlTypes2 = [];
        $authorName = "";
        // either employee, resource or first project (more need to be potentially downloaded if there are parent projects)
        $entityData = null;
        $workingOrderData = null;
        // key is id (raw reportedValue)
        $oversizeItems = [];
        // key is fid
        $files = [];
        // key is pnr
        $employees = [];
        // key is knr_lfdnr
        $partners = [];
        // key is id
        $suppliers = [];
        // key is id (raw reportedValue)
        $tasks = [];
        // key is rnr
        $resources = [];
        // key is year_belegnr
        $invoices = [];
        // key is knr
        $customers = [];

        $documentAuthorId = $documentData['author'];
        $downloadAuthorViaGetEmployees = (int)$documentAuthorId > 0;
        if ($downloadAuthorViaGetEmployees) {
            $pnr = $documentAuthorId;
            $multiCurlUrls2[] = "$this->base/v3/employees/$pnr?showRelatedProject=false";
            $multiCurlTypes2[] = [
                "type" => MultiCurlType::AuthorEmployee,
                'pnr' => $pnr
            ];
        } else {
            // note that this only works if $documentAuthorId is an integer and not a string
            $partnerIds = self::decodeIdByCantorTools($documentAuthorId);
            $knr = (int)$partnerIds[0];
            $lfdnr = (int)$partnerIds[1];
            $multiCurlUrls2[] = "$this->base/v1/partners/select/$knr?lfdnr=$lfdnr";
            $multiCurlTypes2[] = [
                "type" => MultiCurlType::AuthorPartner,
                'knr' => $knr,
                'lfdnr' => $lfdnr
            ];
        }

        $isAuthorSameAsRelType = false;
        if ($relType == 'resource') {
            $rnr = $documentData['documentRelKey1'];
            $multiCurlUrls2[] = "$this->base/v1/resources/select/$rnr";
            $multiCurlTypes2[] = [
                "type" => MultiCurlType::EntityDataResource,
                'rnr' => $rnr,
            ];
        } elseif ($relType == 'employee') {
            $pnr = $documentData['documentRelKey1'];
            // check if this employee is already downloaded from the URL above
            if ($pnr == $documentAuthorId && $downloadAuthorViaGetEmployees) {
                $isAuthorSameAsRelType = true;
            } else {
                $multiCurlUrls2[] = "$this->base/v3/employees/$pnr?showRelatedProject=false";
                $multiCurlTypes2[] = [
                    "type" => MultiCurlType::EntityDataEmployee,
                    "pnr" => $pnr
                ];
            }
        } else {
            $projectNo = $documentData['documentRelKey1'];
            $partial = "projectName,customerNo,projectSiteAddress,projectSiteZipCode,projectSiteCity,projectValidStartDate,"
                . "customerName,parentId,projectStatus,externalProjectNo,technicalContactDisplayName,technicalContactKey";
            $multiCurlUrls2[] = "$this->base/v3/projects?filter[projectNo][eq]=$projectNo&partial=$partial";
            $multiCurlTypes2[] = [
                "type" => MultiCurlType::EntityDataProject
            ];
        }

        $isOversizeFromWO = Request::isRoute("KsbgChecklistOversizes");
        $isProjectRelType = !($relType == 'resource' || $relType == 'employee');
        if ($isProjectRelType || $isOversizeFromWO) {
            $projectNo = $documentData['documentRelKey1'];
            $workingOrderNo = $documentData['documentRelKey2'];
            if ($projectNo && $workingOrderNo && is_numeric($projectNo) && is_numeric($workingOrderNo)) {
                $multiCurlUrls2[] = "$this->base/v3/workingorders?filter[projectNo][eq]=$projectNo&filter[workingOrderNo][eq]=$workingOrderNo";
                $multiCurlTypes2[] = ["type" => MultiCurlType::WorkingOrderData];
            }
        }

        unset($child);
        foreach ($documentData['children'] as $child) {
            $type = strtoupper($child['type']);
            if (in_array($type, [SchemaTypes::PHOTO, SchemaTypes::GRAPHICAL_MEASUREMENT, SchemaTypes::SIGNATURE_FIELD])) {
                $multiCurlUrls2[] = "$this->base/v2/files?filter[documentationDocumentId][eq]={$documentData['documentId']}";
                $multiCurlTypes2[] = ['type' => MultiCurlType::AllDocumentFiles];
                break;
            }
        }

        $this->prepareUrlsForDocumentChildren($documentData['children'], $multiCurlUrls2, $multiCurlTypes2);

        $multiCurlResponses2 = $this->curl->_multi_call('get', $multiCurlUrls2, [], PrintoutHelper::getHeadersForApiCalls());
        for ($i = 0; $i < count($multiCurlResponses2); $i++) {
            $statusCode = $multiCurlResponses2[$i]['statusCode'];
            $response = $multiCurlResponses2[$i]['response'];
            $typeWithData = $multiCurlTypes2[$i];
            $type = $typeWithData['type'];
            // Do not die on File type. The API Call usually fails when the file has been deleted or is missing for any other reason.
            // The customers rather want a working pdf, so we just ignore the file in that case.
            if ($statusCode != 200 && $type != MultiCurlType::AllDocumentFiles) {
                die_with_response_code(message: 'GET ' . $multiCurlUrls2[$i] . " failed with " . $statusCode . "\n" . print_r($response, true));
            }
            if ($type === MultiCurlType::AllDocumentFiles) {
                foreach ($response as $file) {
                    $fid = $file['fileID'];
                    $files[$fid] = $file;
                    PrintoutHelper::$downloadedFilesForDocumentMerging[$fid] = [
                        "filePath" => $file['filePath'],
                        "mimeType" => $file['mimeType']
                    ];
                }
            } else if ($type === MultiCurlType::AuthorEmployee) {
                $authorName = $response['displayName'];
                $employees[$typeWithData['pnr']] = $response;

            } else if ($type === MultiCurlType::AuthorPartner) {
                $authorName = $response['firstName'] . " " . $response['lastName'];
                $partners[$typeWithData['knr'] . '_' . $typeWithData['lfdnr']] = $response;

            } else if ($type === MultiCurlType::EntityDataResource) {
                $entityData = $response;
                $resources[$typeWithData['rnr']] = $response;

            } else if ($type === MultiCurlType::EntityDataEmployee) {
                $entityData = $response;
                $employees[$typeWithData['pnr']] = $response;

            } else if ($type === MultiCurlType::EntityDataProject) {
                $entityData = $this->downloadProjectData($documentData['documentRelKey1'], $response[0]);

            } else if ($type === MultiCurlType::WorkingOrderData) {
                // this is optional
                if (count($response) >= 1 && $response[0]) {
                    $workingOrderData = $response[0];
                } else {
                    $workingOrderData = null;
                }

            } else if ($type == MultiCurlType::OversizeItems) {
                $oversizeItems[$typeWithData['id']] = $response;

            } else if ($type == MultiCurlType::Employee) {
                $employees[$typeWithData['pnr']] = $response;

            } else if ($type == MultiCurlType::Partner) {
                $partners[$typeWithData['knr'] . '_' . $typeWithData['lfdnr']] = $response;

            } else if ($type == MultiCurlType::Supplier) {
                $suppliers[$typeWithData['id']] = $response;

            } else if ($type == MultiCurlType::Task) {
                $tasks[$typeWithData['id']] = $response[0];

            } else if ($type == MultiCurlType::Resource) {
                $resources[$typeWithData['rnr']] = $response;

            } else if ($type == MultiCurlType::Invoice) {
                $invoices[$typeWithData['year'] . '_' . $typeWithData['belegnr']] = $response;

            } else if ($type == MultiCurlType::Customer) {
                $customers[$typeWithData['knr']] = $response;
            }
        }

        if ($isAuthorSameAsRelType) {
            $entityData = ["displayName" => $authorName];
        }

        $isEpGpHidden = ($hideEpGp === 'true') || ($hideEpGp === '1');
        $data = $this->combineData($documentData, $entityData, $workingOrderData,
            $schemaData, $photoOnSameRow, $highDefinitionImages, $staticClassAndFunctionForPostProcessing,
            $relType, $isEpGpHidden, $isOversizeFromWO, $logo ?? "", $authorName, $oversizeItems,
            $files, $employees, $partners, $suppliers, $tasks, $resources, $invoices, $customers, $oversizeItemStates);
        $data['hideEpGp'] = $isEpGpHidden;
        $data['isOversizeFromWO'] = $isOversizeFromWO;
        $data['fullDocument'] = $documentData;
        return $data;
    }

    /**
     * Mimic how getData() iterates over the children and fill the multi curl URLs.
     */
    private function prepareUrlsForDocumentChildren(array $documentChildren, array &$multiCurlUrls, array &$multiCurlTypes): void
    {
        foreach ($documentChildren as $v) {
            $type = strtoupper($v['type']);
            if ($type === SchemaTypes::MEASUREMENT) {
                $oversizeItemIds = $v['reportedValues'];
                foreach ($oversizeItemIds as $oversizeItemId) {
                    $oversizeIdParts = explode('-', $oversizeItemId);
                    $billingServiceSheetNo = $oversizeIdParts[0];
                    $billingServiceSheetVersionNo = $oversizeIdParts[1];
                    $billingServiceSheetItemNo = $oversizeIdParts[2];
                    $url = "$this->base/v1/oversizes/items/$billingServiceSheetNo/$billingServiceSheetVersionNo/$billingServiceSheetItemNo";
                    if (!in_array($url, $multiCurlUrls)) {
                        $multiCurlUrls[] = $url;
                        $multiCurlTypes[] = [
                            "type" => MultiCurlType::OversizeItems,
                            "id" => $oversizeItemId
                        ];
                    }
                }
            } else {
                switch ($type) {
                    case SchemaTypes::EMPLOYEE_SELECTOR:
                        foreach ($v['reportedValues'] ?? [] as $pnr) {
                            $url = "$this->base/v3/employees/$pnr?showRelatedProject=false";
                            if (!in_array($url, $multiCurlUrls)) {
                                $multiCurlUrls[] = $url;
                                $multiCurlTypes[] = [
                                    "type" => MultiCurlType::Employee,
                                    "pnr" => $pnr
                                ];
                            }
                        }
                        break;

                    case SchemaTypes::PARTNER_SELECTOR:
                        foreach ($v['reportedValues'] ?? [] as $reportedValue) {
                            list($knr, $lfdnr) = explode("-", $reportedValue);
                            if ($knr != "" && $lfdnr != "") {
                                $url = "$this->base/v1/partners/select/$knr?lfdnr=$lfdnr";
                                // note that this check guards duplicated partner download from AuthorPartner
                                if (!in_array($url, $multiCurlUrls)) {
                                    $multiCurlUrls[] = $url;
                                    $multiCurlTypes[] = [
                                        "type" => MultiCurlType::Partner,
                                        "knr" => $knr,
                                        "lfdnr" => $lfdnr
                                    ];
                                }
                            }
                        }
                        break;

                    case SchemaTypes::SUPPLIER_SELECTOR:
                        foreach ($v['reportedValues'] ?? [] as $id) {
                            $url = "$this->base/v1/addresses/$id";
                            if (!in_array($url, $multiCurlUrls)) {
                                $multiCurlUrls[] = $url;
                                $multiCurlTypes[] = [
                                    "type" => MultiCurlType::Supplier,
                                    "id" => $id
                                ];
                            }
                        }
                        break;

                    case SchemaTypes::TASK_SELECTOR:
                        foreach ($v['reportedValues'] ?? [] as $reportedValue) {
                            $query = urlencode($reportedValue);
                            $url = "$this->base/v1/tasks/select?task=$query";
                            if (!in_array($url, $multiCurlUrls)) {
                                $multiCurlUrls[] = $url;
                                $multiCurlTypes[] = [
                                    "type" => MultiCurlType::Task,
                                    "id" => $reportedValue
                                ];
                            }
                        }
                        break;

                    case SchemaTypes::CUSTOMER_SELECTOR:
                        foreach ($v['reportedValues'] ?? [] as $knr) {
                            $url = "$this->base/v1/addresses/$knr";
                            if (!in_array($url, $multiCurlUrls)) {
                                $multiCurlUrls[] = $url;
                                $multiCurlTypes[] = [
                                    "type" => MultiCurlType::Customer,
                                    "knr" => $knr
                                ];
                            }
                        }
                        break;

                    case SchemaTypes::RESOURCE_SELECTOR:
                        foreach ($v['reportedValues'] ?? [] as $rnr) {
                            $url = "$this->base/v1/vehicles/select_resource/$rnr";
                            if (!in_array($url, $multiCurlUrls)) {
                                $multiCurlUrls[] = $url;
                                $multiCurlTypes[] = [
                                    "type" => MultiCurlType::Resource,
                                    "rnr" => $rnr
                                ];
                            }
                        }
                        break;

                    case SchemaTypes::INVOICE_SELECTOR:
                        foreach ($v['reportedValues'] ?? [] as $reportedValue) {
                            list($belegnr, $year) = explode("-", $reportedValue);
                            $url = "$this->base/v1/invoices/$year/$belegnr";
                            if (!in_array($url, $multiCurlUrls)) {
                                $multiCurlUrls[] = $url;
                                $multiCurlTypes[] = [
                                    "type" => MultiCurlType::Invoice,
                                    "year" => $year,
                                    "belegnr" => $belegnr
                                ];
                            }
                        }
                        break;
                }
            }
        }
    }

    private function combineData(
        array       $documentData,
        array       $entityData,
        array|null  $workingOrderData,
        array       $schemaData,
        string|null $photoOnSameRow,
        string|null $highDefinitionImages,
        string|null $staticClassAndFunctionForPostProcessing,
        string      $relType,
        bool        $isEpGpHidden,
        bool        $isOversizeFromWO,
        string      $logo,
        string      $authorName,
        array       $oversizeItems,
        array       $files,
        array       $employees,
        array       $partners,
        array       $suppliers,
        array       $tasks,
        array       $resources,
        array       $invoices,
        array       $customers,
        string|null $oversizeItemStates
    )
    {
        $templateData = [
            'schema' => $schemaData,
            'logo' => $logo,
            'authorName' => $authorName,
            'strings' => $this->getStrings($relType),
            'colors' => $this->getColors(),
            'photoOnSameRow' => $photoOnSameRow === "1",
            'document' => [
                'id' => $documentData['documentId']
            ]
        ];

        if ($highDefinitionImages === "1") {
            // the value is not important since the template only checks if the key is present
            $templateData['highDefinitionImages'] = true;
        }

        $dateTimeInUtc = DateTime::createFromFormat('Y-m-d\TH:i:s+', $documentData['documentCreatedOn'], new DateTimeZone('UTC'));
        if ($dateTimeInUtc) {
            $dateTimeInLocal = $dateTimeInUtc->setTimezone(new DateTimeZone('Europe/Vienna'));
            $templateData['document']['creationDate'] = $dateTimeInLocal;
        }

        $reportedValues = [];
        $mappedPhotos = [];
        $signatures = [];

        // this is the third multi curl sequence
        $multiCurlUrls3 = [];
        $multiCurlTypes3 = [];
        $oversizes = [];
        $photos = [];
        $oversizesFromWoData = [];
        // is not always required
        $customer = null;
        $headline = null;

        // transform positions (= children) into item-arrays for handling in template
        $statesParam = $oversizeItemStates ?: 'reported';
        $statesFilter = array_map('trim', explode(',', strtolower($statesParam)));
        foreach ($documentData['children'] as $v) {
            // skip fields of type info
            $type = strtoupper($v['type']);
            if ($type === SchemaTypes::INFO) {
                continue;
            }
            if ($type == SchemaTypes::SIGNATURE_FIELD) {
                $fid = $v['reportedValues'][0];
                if (!isset($files[$fid])) {
                    // This usually means the file has been deleted or is missing for any other reason.
                    // The customers rather want a working pdf, so we just ignore the file in that case.
                    continue;
                }
                $signatures[] = [
                    'schema_position_title' => $v['title'],
                    'filePath' => $files[$fid]['filepath']
                ];
            }
            if (in_array($type, [SchemaTypes::PHOTO, SchemaTypes::GRAPHICAL_MEASUREMENT, SchemaTypes::SIGNATURE_FIELD])) {
                foreach ($v['reportedValues'] as $fid) {
                    if (!isset($files[$fid])) {
                        // This usually means the file has been deleted or is missing for any other reason.
                        // The customers rather want a working pdf, so we just ignore the file in that case.
                        continue;
                    }
                    $photo = $files[$fid];

                    // add schema_position_title
                    if (isset($v['displayInside']) && $v['displayInside'] && $v['displayInside'] !== $v['id'] && $v['displayInside'] !== $v['parentId']) {
                        $displayInsideTargetTitle = $reportedValues[$v['parentId']]['children'][$v['displayInside']]['name'];
                        $photo['schema_position_title'] = $displayInsideTargetTitle;
                    } else {
                        $photo['schema_position_title'] = $v['title'];
                    }
                    // pass on ids for later assignment
                    $photo['schema_position_parentId'] = $v['parentId'];
                    $photo['schema_position_id'] = $v['id'];

                    if (in_array($type, [SchemaTypes::PHOTO, SchemaTypes::GRAPHICAL_MEASUREMENT])) {
                        $photos[] = $photo;
                        $targetId = empty($v['displayInside']) || $v['displayInside'] === $v['parentId'] || $v['displayInside'] === $v['id']
                            ? $v['parentId'] : $v['displayInside'];

                        $mappedPhotos[] = [
                            'id' => $targetId,
                            'photo' => $photo
                        ];
                    }
                }
            } else if ($type === SchemaTypes::HEADLINE) {
                $headline = array(
                    "name" => $v['title'],
                    "type" => $type,
                    "children" => array(),
                    "id" => $v['id'],
                    "parentId" => $v['parentId']
                );
                $reportedValues[$v['id']] = $headline;
            } else {
                // handle all other types, including processing of displayInside information
                // convert date to DD.MM.YYYY
                if ($type === SchemaTypes::DATE) {
                    $datetime = DateTime::createFromFormat('Y-m-d', substr($v['reportedValues'][0], 0, 10));
                    $v['reportedValues'][0] = $datetime->format('d.m.Y');
                }
                $newReportedValues =
                    $this->mapDownloadedEntitiesToReportedValues($type, $v['reportedValues'] ?? null,
                        $employees, $partners, $suppliers, $tasks, $resources, $invoices, $customers);
                $item = array(
                    "name" => $v['title'],
                    "reportedValues" => $newReportedValues,
                    "reportedValue" => $v['reportedValue'] ?? null,
                    "type" => $type,
                    "schemaPositionId" => $v['id'],
                    "printIndex" => $v['printIndex'],
                    "printText" => $v['printText'] ?? null,
                    "parentId" => $v['parentId']
                );
                if ($headline) {
                    if (isset($v['displayInside'])) {
                        $reportedValues[$v['parentId']]['children'][$v['displayInside']]['displayInsideObjects'][] = $item;
                    } else {
                        $reportedValues[$v['parentId']]['children'][$v['id']] = $item;
                    }
                } // if displayInside is set, the value is stored in its reference
                elseif (isset($v['displayInside']) && $v['displayInside']) {
                    $reportedValues[$v['displayInside']]['displayInsideObjects'][] = $item;
                } else {
                    $reportedValues[$v['id']] = $item;
                }
            }

            if ($type === SchemaTypes::MEASUREMENT) {
                $oversizeItemIds = $v['reportedValues'];
                foreach ($oversizeItemIds as $oversizeItemId) {
                    $oversizeItem = $oversizeItems[$oversizeItemId];
                    if (!$oversizeItem) {
                        continue;
                    }

                    // https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/299#note_593935
                    if ($isEpGpHidden && $oversizeItem['itemType'] === 'TS') {
                        continue;
                    }

                    // https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/311#note_503519
                    $exception = ['TH', 'TS'];
                    if ($oversizeItem['measuringUnitKey'] === ""
                        && $oversizeItem['unitPrice'] === 0
                        && !in_array($oversizeItem['itemType'], $exception)) {

                        $multiCurlUrls3[] = "$this->base/directoryofservices/items?compoundKey=" . urlencode($oversizeItem['serviceItemNo']);
                        $multiCurlTypes3[] = [
                            "type" => MultiCurlType::DirectoryOfServiceItems,
                            "oversizeItemId" => $oversizeItemId,
                        ];

                    } else {
                        $oversizeItem['unitPrice'] = (float)$oversizeItem['unitPrice'];
                    }

                    $oversizes[$oversizeItemId] = $oversizeItem;
                }
            }
        }

        $templateData['signatures'] = $signatures;
        $templateData['reportedValues'] = $reportedValues;
        $templateData['photos'] = $photos;
        $templateData['oversizes'] = $oversizes;

        foreach ($templateData['reportedValues'] as $reportedVal) {
            foreach ($reportedVal['reportedValues'] ?? [] as &$vv) {
                $vv = nl2br($vv);
            }
            foreach ($reportedVal['displayInsideObjects'][0]['reportedValues'] ?? [] as &$rVal) {
                $rVal = nl2br($rVal);
            }
        }

        if ($relType !== 'resource' && $relType !== 'employee' && $entityData['customerNo']) {
            $knr = $entityData['customerNo'];
            if (array_key_exists($knr, $customers)) {
                $customer = $customers[$knr];
            } else {
                $multiCurlUrls3[] = "$this->base/v1/addresses/$knr";
                $multiCurlTypes3[] = [
                    "type" => MultiCurlType::Customer,
                ];
            }
        }

        if ($isOversizeFromWO && !empty($workingOrderData)) {
            $ktr = $workingOrderData['projectNo'];
            $wo = $workingOrderData['workingOrderNo'];
            $multiCurlUrls3[] = "$this->base/v1/oversizes/ALL?" .
                "include=positions&" .
                "filter[projectNo][eq]=$ktr&" .
                "filter[workingOrderNo][eq]=$wo&" .
                "latestVersion=true";
            $multiCurlTypes3[] = [
                "type" => MultiCurlType::OversizesAll
            ];
        }

        $multiCurlResponses3 = $this->curl->_multi_call('get', $multiCurlUrls3, [], PrintoutHelper::getHeadersForApiCalls());
        for ($i = 0; $i < count($multiCurlResponses3); $i++) {
            $statusCode = $multiCurlResponses3[$i]['statusCode'];
            $response = $multiCurlResponses3[$i]['response'];
            if ($statusCode != 200) {
                die_with_response_code(message: 'GET ' . $multiCurlUrls3[$i] . " failed with " . $statusCode . "\n" . print_r($response, true));
            }
            $typeWithData = $multiCurlTypes3[$i];
            $type = $typeWithData['type'];

            if ($type === MultiCurlType::Customer) {
                if (array_is_list($response)) {
                    $customer = $response[0];
                } else {
                    $customer = $response;
                }
            } elseif ($type === MultiCurlType::OversizesAll) {
                $oversizeAllItems = $response;
                $oversizeAllItems = array_filter($oversizeAllItems, fn($item) => !empty($item['positions']));
                foreach ($oversizeAllItems as $item) {
                    // Apply dynamic filtering based on allowed states
                    $positions = array_filter(
                        $item['positions'],
                        fn($pos) => in_array(strtolower($pos['state']), $statesFilter)
                    );
                    $oversizesFromWoData = array_merge($oversizesFromWoData, $positions);
                }
            } elseif ($type === MultiCurlType::DirectoryOfServiceItems) {
                $directoryOfServiceItems = $response;
                if (count($directoryOfServiceItems) >= 1) {
                    $oversizeItemId = $typeWithData['oversizeItemId'];
                    $first = $directoryOfServiceItems[0];
                    foreach ($templateData['oversizes'] as $key => &$value) {
                        if ($key === $oversizeItemId) {
                            $value['measuringUnitKey'] = $first['measuringUnitKey'];
                            $value['unitPrice'] = $first['unitPrice'];
                        }
                    }
                }
            }
        }

        $templateData['oversizesFromWoData'] = $oversizesFromWoData;
        $this->fillInEntityData($relType, $entityData, $workingOrderData, $customer, $templateData);

        // call by string if the function exists
        if (is_callable($staticClassAndFunctionForPostProcessing)) {
            $classAndFunction = explode('::', $staticClassAndFunctionForPostProcessing);
            $templateData = forward_static_call($classAndFunction, $templateData);
        }

        $templateData['mappedPhotos'] = $mappedPhotos;
        return $templateData;
    }

    private function fillInEntityData(
        string $relType, array $entityData, array|null $workingOrderData, array|null $customer, array &$templateData): void
    {
        if ($relType === 'resource') {
            $templateData['entity']['name'] = $entityData['kurzname'];
        } elseif ($relType === 'employee') {
            $templateData['entity']['name'] = $entityData['displayName'];
        } else {
            $templateData['entity']['name'] = $entityData['rootProjectName'];
            if (isset($entityData['composedProjectName'])) {
                $templateData['entity']['subProjectName'] = $entityData['composedProjectName'] . " · " . $entityData['projectName'];
            } else {
                $templateData['entity']['subProjectName'] = $entityData['projectName'];
            }

            $templateData['entity']['customerName'] = $entityData['customerName'];

            $templateData['entity']['customerAddress'] = '';
            if ($entityData['customerNo']) {
                $templateData['entity']['customerAddress'] =
                    (!empty($customer['address']) ? $customer['address'] . ', ' : '') .
                    (!empty($customer['postcode']) ? $customer['postcode'] . ' ' : '') .
                    $customer['city'];
            }

            $templateData['entity']['address'] =
                (!empty($entityData['projectSiteAddress']) ? $entityData['projectSiteAddress'] . ', ' : '') .
                (!empty($entityData['projectSiteZipCode']) ? $entityData['projectSiteZipCode'] . ' ' : '') .
                $entityData['projectSiteCity'];

            $templateData['entity']['status'] = $entityData['projectStatus'];

            if (method_exists('PrintoutsHakakuecheCloud', 'KsbgChecklist_combineData_getExternalNo') && $workingOrderData) {
                $externalNo = PrintoutsHakakuecheCloud::KsbgChecklist_combineData_getExternalNo($workingOrderData);
            } else {
                $externalNo = $entityData['externalProjectNo'];
            }

            $templateData['entity']['externalNo'] = $externalNo;

            if (isset($entityData['technicalContactDisplayName']) &&
                strlen(trim($entityData['technicalContactDisplayName'])) > 0 &&
                trim($entityData['technicalContactDisplayName'])) {

                $templateData['entity']['projectManager'] = $entityData['technicalContactDisplayName'];
                $templateData['entity']['projectManagerId'] = $entityData['technicalContactKey'];

            } else {
                $templateData['entity']['projectManager'] = 'nicht hinterlegt';
            }
        }
    }

    /**
     * Provides custom titles for the static top part of the document
     *
     * @return string[]
     */
    private function getStrings($documentRelType): array
    {
        if ($this->principal == 'keramiko_2020') {
            $strings = [
                'title' => 'Project',
                'customerTitle' => 'Customer',
                'projectSiteAddressTitle' => 'Site Address',
                'checklistIdTitle' => 'Checklist No.',
                'projectManagerTitle' => 'Site Management',
                'authorTitle' => 'Created By',
                'creationDateTitle' => 'Created On',
                'signatureHeadline' => 'Signatures',
                'yes' => 'Yes',
                'no' => 'No',
                // null provokes the entire section not to be shown in this case
                'sectionTitle' => null
            ];
            if ($documentRelType == 'resource') {
                $strings['title'] = 'Resource';
            } elseif ($documentRelType == '') {
                $strings['title'] = 'Employee';
            }
        } else {
            $strings = [
                'title' => 'Bauvorhaben',
                'customerTitle' => 'Auftraggeber',
                'projectSiteAddressTitle' => 'Baustellenadresse',
                'checklistIdTitle' => 'Checklisten-Nr.',
                'projectManagerTitle' => 'Bauleitung',
                'authorTitle' => 'erstellt von',
                'creationDateTitle' => 'erstellt am',
                'signatureHeadline' => 'Unterschriften',
                'yes' => 'Ja',
                'no' => 'Nein',
                'sectionTitle' => 'Bereich'
            ];
            if ($documentRelType == 'resource') {
                $strings['title'] = 'Ressource';
            } elseif ($documentRelType == 'employee') {
                $strings['title'] = 'Mitarbeiter';
            }
        }

        if (method_exists('PrintoutsHakakuecheCloud', 'C_KsbgChecklist_getStrings')) {
            PrintoutsHakakuecheCloud::C_KsbgChecklist_getStrings($strings);
        }

        return $strings;
    }

    private static function getColors(): array
    {
        return [
            'headline-bg-color' => 'lightgray',
            'headline-border-color' => 'darkgrey',
            'headline-font-color' => 'black'
        ];
    }

    private function mapDownloadedEntitiesToReportedValues(
        string $type, array|null $reportedValues, array $employees, array $partners, array $suppliers, array $tasks, array $resources, array $invoices,
        array  $customers): array
    {
        $emptyDummy = [''];
        if ($reportedValues == null) {
            // the reportedValues are usually always set
            return $emptyDummy;
        }
        $newReportedValues = [];
        if ($type == SchemaTypes::EMPLOYEE_SELECTOR) {
            foreach ($reportedValues as $reportedValue) {
                $employee = $employees[$reportedValue];
                $newReportedValues[] = $employee['displayName'];
            }
            return $newReportedValues;
        } else if ($type == SchemaTypes::PARTNER_SELECTOR) {
            foreach ($reportedValues as $reportedValue) {
                list($knr, $lfdnr) = explode("-", $reportedValue);
                if ($knr != "" && $lfdnr != "") {
                    $partner = $partners[$knr . '_' . $lfdnr];
                    if ($partner) {
                        $newReportedValues[] = $partner['firstName'] . $partner['lastName'];
                    }
                }
            }
            return $newReportedValues;
        } else if ($type == SchemaTypes::SUPPLIER_SELECTOR) {
            foreach ($reportedValues as $reportedValue) {
                $supplier = $suppliers[$reportedValue];
                $newReportedValues[] = $supplier['displayName'];
            }
            return $newReportedValues;
        } else if ($type == SchemaTypes::TASK_SELECTOR) {
            foreach ($reportedValues as $reportedValue) {
                $task = $tasks[$reportedValue];
                $newReportedValues[] = $task['title'];
            }
            return $newReportedValues;
        } else if ($type == SchemaTypes::CUSTOMER_SELECTOR) {
            foreach ($reportedValues as $reportedValue) {
                $customer = $customers[$reportedValue];
                $newReportedValues[] = $customer['displayName'];
            }
            return $newReportedValues;
        } else if ($type == SchemaTypes::RESOURCE_SELECTOR) {
            foreach ($reportedValues as $reportedValue) {
                $resource = $resources[$reportedValue];
                $newReportedValues[] = $resource['kurzname'];
            }
            return $newReportedValues;
        } else if ($type == SchemaTypes::INVOICE_SELECTOR) {
            foreach ($reportedValues as $reportedValue) {
                list($belegnr, $year) = explode("-", $reportedValue);
                $invoice = $invoices[$year . '_' . $belegnr];
                /** @noinspection SpellCheckingInspection */
                $newReportedValues[] = sprintf("%s%s%s%s%s%s%s%s",
                    $invoice['info'] ?? '',
                    isset($invoice['kname']) ? (' (' . $invoice['kname'] . ')') : '',
                    isset($invoice['kaddr1']) ? ' ' . $invoice['kaddr1'] : '',
                    isset($invoice['kaddr2']) ? ' ' . $invoice['kaddr2'] : '',
                    isset($invoice['kplz']) ? ' ' . $invoice['kplz'] : '',
                    isset($invoice['kort']) ? (' ' . $invoice['kort'] . ', ') : '',
                    isset($invoice['brutto']) ? '' . $invoice['brutto'] : '',
                    isset($invoice['dsymbol']) ? ' ' . $invoice['dsymbol'] : ''
                );
            }
            return $newReportedValues;
        }
        return $reportedValues;
    }
}
