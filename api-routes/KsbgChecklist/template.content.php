<?php
/** @noinspection DuplicatedCode */
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_KsbgChecklist())->getData(
        schemaId: $_GET['schemaId'] ?? null,
        documentId: $_GET['documentId'] ?? null,
        highDefinitionImages: $_GET['highDefinitionImages'] ?? null,
        photoOnSameRow: $_GET['photoOnSameRow'] ?? null,
        oversizeItemStates: $_GET['oversizeItemStates'] ?? null,
        staticClassAndFunctionForPostProcessing: $_GET['staticClassAndFunctionForPostProcessing'] ?? null,
        hideEpGp: $_GET['hideEpGp'] ?? null
    );
}

$isProjectOrWo = $data['fullDocument']['documentRelType'] !== 'resource' && $data['fullDocument']['documentRelType'] !== 'employee';

function isHinweisschreiben($schema): bool
{
    if ($schema) {
        return str_contains($schema['description'], 'Hinweisschreiben');
    }
    return false;
}

function isMitteilungAnDieAuftraggeberIn($schema): bool
{
    if ($schema) {
        return str_contains($schema['description'], 'Mitteilung an den Auftraggeber');
    }
    return false;
}

function commonOversizeTable($oversize, bool $hideEpGp, float &$totalGp): void
{
    $gp = (float)$oversize['unitPrice'] * $oversize['quantity'];
    $totalGp += $gp;
    $colspan = $hideEpGp ? '3' : '5';

    $lfdNr = $oversize['billingServiceSheetItemId'];
    $bezeichnung = $oversize['description'];
    $menge = $oversize['quantity'] ?: "";
    $einheit = $oversize['measuringUnitKey'];

    echo "<tr>";
    echo "<td>$lfdNr</td>";

    if ($oversize['itemType'] === 'TH') {
        echo "<td colspan='$colspan'>$bezeichnung</td>";
    } elseif ($oversize['itemType'] === 'TS') {
        $tsValue = $oversize['unitPrice'] > 0 ? $oversize['unitPrice'] : $totalGp;
        echo "<td colspan='$colspan' style='text-align: right;'> " . number_format($tsValue, 2, ',', '.') . "  €</td>";
    } else {
        echo "<td>$bezeichnung</td>";
        echo "<td style='text-align: right'>$menge</td>";
        if (!$hideEpGp) {
            $ep = number_format((float)$oversize['unitPrice'], 2, ',', '.') . ' €';
            echo "<td style='width: 90px; text-align: right'>$ep</td>";
        }
        echo "<td>$einheit</td>";
        if (!$hideEpGp) {
            $gpFormatted = number_format((float)$oversize['unitPrice'] * $oversize['quantity'], 2, ',', '.') . ' €';
            echo "<td style='width: 90px; text-align: right'>$gpFormatted</td>";
        }
    }
    echo "</tr>";
}

function generateOversizeFromWOTable($allOversizes, bool $hideEpGp): void
{
    echo "<table class='table-full arial table-oversizes' style='margin-bottom: 20px !important;'><tr>";
    $columns = $hideEpGp ? ["lfd. Nr.", "Bezeichnung", "Menge", "Einheit"] : ["lfd. Nr.", "Bezeichnung", "Menge", "EP", "Einheit", "GP"];
    foreach ($columns as $column) {
        if ($column !== 'Bezeichnung') {
            echo "<th style='padding: 5px; width: 10%;'>$column</th>";
        } else {
            echo "<th style='padding: 5px'>$column</th>";
        }
    }
    echo "</tr>";
    $totalGp = 0;
    foreach ($allOversizes as $oversize) {
        commonOversizeTable($oversize, $hideEpGp, $totalGp);
    }
    echo '</table>';
}

function generateOversizeTable($allOversizes, $oversizeIds, bool $hideEpGp): void
{
    echo "<table class='table-full arial table-oversizes'><tr>";
    $columns = $hideEpGp ? ["lfd. Nr.", "Bezeichnung", "Menge", "Einheit"] : ["lfd. Nr.", "Bezeichnung", "Menge", "EP", "Einheit", "GP"];
    foreach ($columns as $column) {
        echo "<th style='padding: 5px'>$column</th>";
    }
    echo "</tr>";
    $totalGp = 0;
    foreach ($oversizeIds as $id) {
        $parts = explode("-", $id);
        foreach ($allOversizes as $oversize) {
            if ($oversize['billingServiceSheetNo'] !== (int)$parts[0] ||
                $oversize['billingServiceSheetVersionNo'] !== (int)$parts[1] ||
                $oversize['billingServiceSheetItemId'] !== (int)$parts[2]) {
                continue;
            }
            commonOversizeTable($oversize, $hideEpGp, $totalGp);
        }
    }
    echo '</table>';
}

// Store all images already displayed to not print the same image twice for the same row.
// This is the easiest solution, so every new row under a Position headline displays unique pictures.
// One could also unset displayed images from $data['mappedPhotos'], but this is easier.
//
// There can still be bugs since this solution does not properly keep track to what row the image belongs.
// But, the schema.json also does not reflect this, so this should work in many scenarios.
$GLOBALS['printedImages'] = [];

function getImages($reportedValue, $data): array
{
    $images = [];
    foreach ($data['mappedPhotos'] as $photo) {
        $match = false;
        if (isset($reportedValue['name']) && $photo['id'] === ($reportedValue['parentId'] ?? null)) {
            $match = true;
        } elseif (isset($reportedValue['schemaPositionId']) && $photo['id'] === $reportedValue['schemaPositionId']) {
            $match = true;
        }
        if ($match && !in_array($photo, $GLOBALS['printedImages'], true)) {
            $GLOBALS['printedImages'][] = $photo;
            $images[] = $photo['photo']['filePath'];
        }
    }
    return $images;
}

function generateRow($reportedValue, $data, $photoOnSameRow, $strings): void
{
    $images = ($data && $photoOnSameRow) ? getImages($reportedValue, $data) : [];

    echo "<tr><td style='border: 1px solid lightgrey; font-size:13px;padding: 5px; width:30%' class='vAlign arial'><b>";

    // set title
    if (method_exists('PrintoutsHakakuecheCloud', 'KsbgChecklist_template_process_positionsTitle')) {
        echo PrintoutsHakakuecheCloud::KsbgChecklist_template_process_positionsTitle($reportedValue);
    } else {
        if (isset($reportedValue['name'])) {
            echo $reportedValue['name'];
        }
    }

    echo "</b></td>";

    $tdAttrs = "style='border:1px solid lightgrey;font-size:13px;padding:5px' class='vAlign arial'";
    $imageCount = count($images);
    if (!$photoOnSameRow || $imageCount === 0) {
        echo "<td colspan='2' $tdAttrs>";
    } elseif ($imageCount === 1) {
        echo "<td $tdAttrs>";
    } else {
        // $imageCount > 1
        echo "<td colspan='2' $tdAttrs>";
    }

    // This is a fallback in case the convoluted logic below doesn't echo anything.
    // If true, just display everything in reportedValues.
    //
    // for https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/624

    $hasFilledTd = false;

    if ($data && !isHinweisschreiben($data['schema'])) {
        if (isset($reportedValue['type']) && strtoupper($reportedValue['type']) === SchemaTypes::CHECKBOX) {

            if (PrintoutHelper::isCheckboxTicked($reportedValue)) {
                echo $strings['yes'];
            } else {
                echo $strings['no'];
            }

            $hasFilledTd = true;

        } else {

            if (method_exists('PrintoutsHakakuecheCloud', 'KsbgChecklist_template_process_positionsValue')) {
                echo PrintoutsHakakuecheCloud::KsbgChecklist_template_process_positionsValue($reportedValue, $strings);
                $hasFilledTd = true;

            } else {
                if (isset($reportedValue['type']) && strtoupper($reportedValue['type']) === SchemaTypes::MEASUREMENT) {
                    generateOversizeTable($data['oversizes'], $reportedValue['reportedValues'], $data['hideEpGp']);
                    $hasFilledTd = true;
                } else {
                    if (isset($reportedValue['reportedValues'])) {
                        echo nl2br(implode(',', $reportedValue['reportedValues']));
                        $hasFilledTd = true;
                    }
                }
            }
        }
    }

    if (!$hasFilledTd && isset($reportedValue['reportedValues'])) {
        // exclude for https://gitlab.baubuddy.de/external/digu/printouts/-/issues/15
        if (!(strtoupper($reportedValue['type']) === SchemaTypes::CHECKBOX && isset($reportedValue['printText']))) {
            echo nl2br(implode(", ", $reportedValue['reportedValues']));
        }
    }

    echo "</td>";

    $count = count($images);
    if ($photoOnSameRow && $count === 1) {
        echo '<td class="vAlign arial same-row-image-container">'
            . '<img src="' . $images[0] . '"' . ' alt="Foto" class="same-row-image-one"/>'
            . '</td></tr>';
        return;
    }
    foreach (array_chunk($images, 2) as $pair) {
        echo '<tr style="border:1px solid lightgray">'
            . '<td colspan="3" class="vAlign same-row-images-container" style="padding:5px;">';
        foreach ($pair as $src) {
            if (!empty($src)) {
                echo '<img src="' . $src . '"' . ' alt="Foto" class="same-row-images"/>';
            }
        }
        echo '</td></tr>';
    }

    if ($data && isHinweisschreiben($data['schema'])) {
        echo "<tr>
        <!-- show below in own <tr> if value is added -->
        <td colspan='2' style='border: 1px solid lightgray; font-size:13px;color:black; padding: 5px' class='arial'>";
        $displayText = $reportedValue['printText'] ?: implode(',', $reportedValue['reportedValues']);
        // if no description is found add the values content
        echo $displayText;
        echo "</td>
            </tr>";
    } else {
        if (isset($reportedValue['displayInsideObjects'])) {
            foreach ($reportedValue['displayInsideObjects'] as $object) {
                if (!(isset($object['name']) && isset($object['reportedValues']))) {
                    continue;
                }
                $colspan = 3;
                echo "<tr>
                <!-- show below in own <tr> if value is added -->
                <td colspan='$colspan' style='border: 1px solid lightgray; font-size:13px; color:gray; padding:5px' class='arial'>";
                echo $object['name'] . ": " . implode(', ', $object['reportedValues']);
                echo "</td>
                        </tr>";
            }
        }
    }
}

?>

<html lang="de">
<head>
    <title>KsbgChecklist</title>
    <meta http-equiv=Content-Type content="text/html; charset=utf-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body class="tab-interval">
<div>
    <table class="full-width">
        <tr class="header-row">
            <td class="<?= 'cell-border bg-light padding-5 valign-top ' .
            ($isProjectOrWo ? 'width-40' : 'width-60') ?>">
                <p class="no-spacing" style="tab-stops:right 538.6pt">
                    <!-- for instance: Bauvorhaben -->
                    <span class="font-8"><?= $data['strings']['title'] ?></span>
                </p>
                <p class="no-spacing" style="tab-stops:right 538.6pt">
                    <span class="font-8">&nbsp;</span>
                </p>
                <p class="no-spacing" style="tab-stops:right 538.6pt">
          <span class="font-14">
            <?php if ($isProjectOrWo) {
                echo $data['entity']['externalNo'] . ' ' . $data['entity']['name'];
            } else {
                echo $data['entity']['name'];
            } ?>
          </span>
                </p>
            </td>

            <?php if ($isProjectOrWo) { ?>
                <td class="cell-border-black padding-5 arial valign-top width-30">
                    <p class="no-spacing" style="tab-stops:right 538.6pt">
                        <!-- Auftraggeber -->
                        <span class="font-8"><?= $data['strings']['customerTitle'] ?></span>
                    </p>
                    <p class="no-spacing" style="tab-stops:right 538.6pt">
                        <span class="font-8">&nbsp;</span>
                    </p>
                    <p class="no-spacing" style="tab-stops:right 538.6pt">
                        <span class="font-14"><?= $data['entity']['customerName'] ?></span>
                    </p>
                    <p class="no-spacing font-15" style="padding:2px 0">
                        <?= $data['entity']['customerAddress'] ?>
                    </p>
                </td>
            <?php } ?>

            <td class="align-right width-33">
                <?php
                if (method_exists('PrintoutsKeramikStein', 'selectCorrectLogo')) {
                    echo PrintoutsKeramikStein::selectCorrectLogo(
                        $data['entity']['externalNo'],
                        $data['entity']['projectManagerId'] ?? -1
                    );
                } else {
                    echo '<img style="width:100%" src="' . $data['logo'] . '">';
                }
                ?>
            </td>
        </tr>
    </table>

    <br><br>

    <?php if ($isProjectOrWo) { ?>
        <table class="full-width arial">
            <tr>
                <td colspan="2" class="cell-border-black-no">
                    <?= $data['entity']['address'] ?>
                </td>
                <td class="cell-border-black-no">
                    #<?= $data['document']['id'] ?>
                </td>
            </tr>
            <tr class="small-text">
                <td colspan="2">
                    <!-- Baustellenadresse -->
                    <?= $data['strings']['projectSiteAddressTitle'] ?>
                </td>
                <td>
                    <!-- Checklisten-Nr. -->
                    <?= $data['strings']['checklistIdTitle'] ?>
                </td>
            </tr>
            <tr>
                <td class="cell-border-black-no"><?= $data['entity']['projectManager'] ?></td>
                <td class="cell-border-black-no"><?= $data['authorName'] ?></td>
                <td class="cell-border-black-no">
                    <?php
                    if (is_a($data['document']['creationDate'], 'DateTime')) {
                        echo $data['document']['creationDate']->format('d.m.Y H:i\h');
                    } else {
                        echo date('d.m.Y H:i\h', strtotime($data['document']['creationDate']['date']));
                    }
                    ?>
                </td>
            </tr>
            <tr class="small-text">
                <td><?= $data['strings']['projectManagerTitle'] ?></td>
                <td><?= $data['strings']['authorTitle'] ?></td>
                <td><?= $data['strings']['creationDateTitle'] ?></td>
            </tr>

            <?php if ($data['strings']['sectionTitle']) { ?>
                <tr>
                    <td colspan="3" class="cell-border-black-no">
                        <?= $data['entity']['subProjectName'] ?>
                    </td>
                </tr>
                <tr class="small-text">
                    <td colspan="3"
                        <?php if (isHinweisschreiben($data['schema'])) {
                            echo 'class="cell-border-black-no"';
                        } ?>>
                        <!-- Bereich -->
                        <?= $data['strings']['sectionTitle'] ?><br><br>
                    </td>
                </tr>
            <?php } ?>
        </table>
    <?php } else { ?>
        <table class="full-width arial">
            <tr>
                <td class="cell-border-black-no"><?= $data['authorName'] ?></td>
                <td class="cell-border-black-no">
                    <?php
                    if (is_a($data['document']['creationDate'], 'DateTime')) {
                        echo $data['document']['creationDate']->format('d.m.Y H:i\h');
                    } else {
                        $dateStr = strtotime($data['document']['creationDate']['date']);
                        echo date('d.m.Y H:i\h', $dateStr);
                    }
                    ?>
                </td>
                <td class="cell-border-black-no">#<?= $data['document']['id'] ?></td>
            </tr>
            <tr style="font-size:15px">
                <td><?= $data['strings']['authorTitle'] ?></td>
                <td><?= $data['strings']['creationDateTitle'] ?></td>
                <td><?= $data['strings']['checklistIdTitle'] ?></td>
            </tr>
        </table>
    <?php } ?>

    <?php
    if (isHinweisschreiben($data['schema'])) {
        ?>
        <div class="font-12">Betreff: Hinweisschreiben</div><br>
        <div class="font-11">
            Sehr geehrte Damen und Herren,<br><br>
            <?= $data['schema']['description'] ?><br>
            Wir weisen darauf hin, dass wir für die angeführten Bereiche/Leistungen keine Gewährleistung
            übernehmen bzw. für auftretende Schäden nicht haftbar gemacht werden können, wenn unsere
            Bedenken außer Acht gelassen werden.<br><br>
        </div>
        <?php
    } elseif (isMitteilungAnDieAuftraggeberIn($data['schema'])) {
        ?>
        <div class="font-12">Betreff: Mitteilung an die AuftraggeberIn</div><br>
        <?php
    } else {
        ?>
        <div class="font-12">Betreff: <?= $data['schema']['title'] ?></div><br>
        <?php
    }

    if ($data['isOversizeFromWO'] && !empty($data['oversizesFromWoData'])) {
        generateOversizeFromWOTable($data['oversizesFromWoData'], $data['hideEpGp']);
    }
    ?>

    <table class="firstTable full-width">
        <?php foreach ($data['reportedValues'] as $reportedValue) {
            $colspan = $data['photoOnSameRow'] ? 3 : 2;

            // special hacky handling of Beschreibungsposition in Hinweisschreiben
            if (isHinweisschreiben($data['schema']) && !isset($reportedValue['name'])) {
                $reportedValue['type'] = SchemaTypes::HEADLINE;
                $firstKey = array_keys($reportedValue['children'])[0];
                $reportedValue['name'] = $reportedValue['children'][$firstKey]['name'];
            }

            if (isset($reportedValue['type'])
                && strtoupper($reportedValue['type']) === SchemaTypes::HEADLINE
            ) {
                if (!$reportedValue['children']) {
                    continue;
                }
                ?>
                <tr>
                    <!-- headline -->
                    <td colspan="<?= $colspan ?>" class="headline-cell"
                        style="background-color: <?= $data['colors']['headline-bg-color'] ?>;
                                border:1px solid <?= $data['colors']['headline-border-color'] ?>;
                                color: <?= $data['colors']['headline-font-color'] ?>;">
                        <b>
                            <?php
                            if (method_exists(
                                'PrintoutsHakakuecheCloud',
                                'KsbgChecklist_template_processPositionsHeadline'
                            )) {
                                echo PrintoutsHakakuecheCloud
                                    ::KsbgChecklist_template_processPositionsHeadline($reportedValue);
                            } else {
                                echo $reportedValue['name'];
                            }
                            ?>
                        </b>
                    </td>
                </tr>
                <?php foreach ($reportedValue['children'] as $child) {
                    generateRow($child, $data, $data['photoOnSameRow'], $data['strings']);
                } ?>

                <?php
            } elseif (isset($reportedValue['type'])
                && strtoupper($reportedValue['type']) === SchemaTypes::MEASUREMENT
            ) {
                // see https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/165#note_514888
                generateOversizeTable($data['oversizes'], $reportedValue['reportedValues'], $data['hideEpGp']);

            } else {
                if (count($reportedValue) === 1 && isset($reportedValue['children'])) {
                    foreach ($reportedValue['children'] as $child) {
                        generateRow($child, $data, $data['photoOnSameRow'], $data['strings']);
                    }
                } else {
                    // normal value to show
                    generateRow($reportedValue, null, $data['photoOnSameRow'], $data['strings']);
                }
            }
        } ?>
    </table>
</div>

<?php
function generateFilePath($item, array $data, $i)
{
    if ($item === 'signatures') {
        return $data['signatures'][$i]['filePath'] ?? null;
    }
    if ($item === 'photos') {
        $returnKey = isset($data['highDefinitionImages']) ? 'filePath' : 'thumbPath';
        return $data['photos'][$i][$returnKey] ?? null;
    }
    die('Unsupported $item requested: ' . $item);
}

function openWrapper(string $extraStyles = ''): void
{
    echo '<div style="margin:0;font-family:Arial;page-break-before:always;' . $extraStyles . '">';
}

function closeWrapper(): void
{
    echo '</div>';
}

function renderHeader(string $item, array $data, int $index): void
{
    if ($item === 'photos') {
        if ($index === 0) {
            echo '<div style="background:lightgray;">
                    <p style="margin:0;padding:5px;font-size:20px;">Fotos</p>
                  </div>';
        }
    } else { // signatures
        $bg = $data['colors']['headline-bg-color'] ?? '#ddd';
        $fg = $data['colors']['headline-font-color'] ?? '#000';
        $txt = $data['strings']['signatureHeadline'] ?? 'Signatures';
        echo "<div style=\"background:$bg;color:$fg;padding:5px;\">
                <p style=\"margin:0;font-size:20px;\">$txt</p>
              </div>";
    }
}

function renderPhotoOrSignature(string $item, array $entry, ?string $filePath = null, array $data = []): void
{
    // Title
    $title = $entry['schema_position_title'] ?? '';
    echo '<p style="margin:0;padding:5px;font-size:13px;word-break:break-all;hyphens:auto;">'
        . $title . '</p>';

    // Image
    echo '<div style="flex:1 1 auto; position:relative;min-height:100px;">';
    if ($filePath) {
        echo '<img src="' . $filePath . '" alt="" '
            . 'style="position:absolute;object-fit:contain;width:100%;height:100%;">';
    } elseif ($item === 'signatures') {
        echo '<div style="font-size:2rem;text-align:center;margin-top:40%;">
                  _________________________________________
              </div>';
    }
    echo '</div>';

    // Signatures: date
    if ($item === 'signatures') {
        if (!empty($data['document']['creationDate'])) {
            $dateObj = $data['document']['creationDate'];
            if (is_a($dateObj, 'DateTime')) {
                $dateStr = $dateObj->format('d.m.Y');
            } else {
                $ts = strtotime($dateObj['date'] ?? $dateObj);
                $dateStr = date('d.m.Y', $ts);
            }
            echo '<p style="margin:5px;font-size:10pt;text-align:center;">' . $dateStr . '</p>';
        }
    }

    // Description
    if (!empty($entry['description'])) {
        echo '<p style="margin:5px;font-size:10pt;text-align:center;">'
            . htmlspecialchars($entry['description']) . '</p>';
    }

    // Stamp for signatures
    if ($item === 'signatures' && method_exists('PrintoutsKeramikStein', 'selectCorrectStamp')) {
        echo PrintoutsKeramikStein::selectCorrectStamp(
            $data['entity']['externalNo'],
            $entry['schema_position_title'] ?? ''
        );
    }
}

function renderSignatureOrHdPhotoSinglePerPageWithPageBreakAfter(string $item, array $entries, array $data): void
{
    $total = count($entries);
    foreach ($entries as $i => $entry) {
        openWrapper();
        echo '<div style="height:5mm;"></div>';
        echo '<div style="height:270mm;display:flex;flex-direction:column;border:1px solid lightgray;'
            . 'page-break-inside:avoid;'
            . ($i < $total - 1 ? 'page-break-after:always;' : '')
            . '">';

        renderHeader($item, $data, $i);
        renderPhotoOrSignature($item, $entry, generateFilePath($item, $data, $i), $data);

        echo '</div>';
        closeWrapper();
    }
}

/**
 * Renders non-HD photos two per page in a 2-column layout.
 */
function renderPhotosNonHD(array $photos, array $data): void
{
    $count = count($photos);
    for ($i = 0; $i < $count; $i += 2) {
        openWrapper();
        echo '<div style="height:5mm;"></div>';
        $breakStyle = ($i + 2 < $count)
            ? 'page-break-after:always;page-break-inside:avoid;'
            : 'page-break-inside:avoid;';
        echo '<div style="height:270mm;display:flex;flex-direction:column;border:1px solid lightgray;' . $breakStyle . '">';

        renderHeader('photos', $data, $i);

        echo '<div style="display:flex;flex:1 1 auto;">';
        for ($col = 0; $col < 2; $col++) {
            $idx = $i + $col;
            $style = 'flex:1 1 50%;display:flex;flex-direction:column;padding:5mm;'
                . ($col === 0 ? 'border-right:1px solid lightgray;' : '');
            echo '<div style="' . $style . '">';

            if ($idx < $count) {
                renderPhotoOrSignature('photos', $photos[$idx], generateFilePath('photos', $data, $idx));
            }

            echo '</div>';
        }
        echo '</div></div>';
        closeWrapper();
    }
}

//Main Rendering
$items = ['signatures', 'photos'];
foreach ($items as $item) {
    if (empty($data[$item])) {
        continue;
    }

    // If we've printed all photos inline, never render the Fotos section at the end.
    if ($item === 'photos' && $data['photoOnSameRow']) {
        continue;
    }

    if ($item === 'photos' && !isset($data['highDefinitionImages'])) {
        renderPhotosNonHD($data['photos'], $data);
    } else {
        renderSignatureOrHdPhotoSinglePerPageWithPageBreakAfter($item, $data[$item], $data);
    }
}
?>
</body>
</html>
