<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';

class C_ChecklisteAufzuge
{
    public function getData($schemaId, $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $fullDocument = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl)['fullDocument'];
        $data = [];
        $headLines = [];

        foreach ($fullDocument['children'] as $child) {
            if ($child['type'] == 'headline') {
                $headLines[$child['id']] = $child;
            }
        }

        $headLines[$fullDocument['id']]['title'] = $fullDocument['title'];
        $headLines[$fullDocument['id']]['id'] = $fullDocument['id'];

        foreach ($headLines as $value) {
            foreach ($fullDocument['children'] as $v) {
                if (!isset($v['parentId'])) {
                    continue;
                }
                if ($v['parentId'] == $value['id']) {
                    if ($v['type'] == 'photo') {
                        $data[$value['title']][$v['title']] = $v['filePath'];
                    } else {
                        $data[$value['title']][$v['title']] = $v['reportedValue'] ?? "";
                    }
                }
            }
        }

        $data['logo'] = PrintoutHelper::downloadSettings($curl)['logo'];
        return $data;
    }
}
