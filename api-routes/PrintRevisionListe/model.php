<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_PrintRevisionListe
{
    public function getData($schemaId, $documentId): array
    {
        $data = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $hierarchicalDocument = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $schema = PrintoutHelper::downloadSchema($schemaId, $curl);
        $data['mappedChildren'] = PrintoutHelper::mapDocumentChildrenToStableNumberedIndices(
            $schema, $hierarchicalDocument, $curl);
        $data['projectData'] = PrintoutHelper::downloadProject($hierarchicalDocument['documentRelKey1'],
            'projectNo,projectSiteAddress,projectSiteZipCode,projectSiteCity', $curl);
        return $data;
    }
}