<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Bestellung())->getData($_GET['schemaId'], $_GET['documentId']);
}

function displayMainTable($data): void
{
    $positionCounter = 1;
    foreach ($data['positions'] as $k => $v) {
        if (str_contains($k, 'Position')) {
            if (!isset($v['Titel']))
                continue;
            echo
                '<tr>' .
                '<td>' . $positionCounter . '</td>' .
                '<td>' . $v['Titel'] . '</td>' .
                '<td>' . ($v['Menge'] ?? '') . '</td>' .
                '<td>';
            if (!empty($v['Anhang'])) {
                echo implode('<br>', array_map(fn($fid) => getFileName($data, $fid), $v['Anhang']));
            }
            echo '</td>' .
                '</tr>';
            $positionCounter++;
        }
    }
}

function displayLieferort($data): string
{
    $Lieferort = $data['positions']['Lieferort soll'];
    return match ($Lieferort) {
        'Baustelle' => 'Baustelle ' . PrintoutHelper::formatAddress(
                $data['projectData']['projectSiteAddress'],
                $data['projectData']['projectSiteZipCode'],
                $data['projectData']['projectSiteCity']),
        'Büro' => 'Carl-Bertelsmann-Straße 32, 33332 Gütersloh',
        // https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/457#note_655497
        'Lager GTB' => 'Lager, Chromstraße 128, 33415 Verl',
        'Abholung' => 'Abholung',
        default => $Lieferort,
    };
}

function getFileName(array $data, $fid): string
{
    return $data['fileNames'][$fid];
}

$bestelllosData = $data['positions'];
$supplierData = $data['supplierData'];
?>

<!doctype html>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Bestellung</title>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body>
<header>
    <div class="container-header">
        <div class="text-container">
            <?= $supplierData['title'] ?? ''; ?>
            <br>
            <?= $supplierData['name'] ?? ''; ?>
            <br>
            <?= $supplierData['address'] ?? ''; ?>
            <br>
            <?= ($supplierData['postcode'] ?? '') . ' ' . ($supplierData['city'] ?? ''); ?>
        </div>
        <div class="image-container">
            <div class="container-logo">
                <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/briefkopf.svg") ?>"
                     alt="logo" class="logo">
            </div>
        </div>
    </div>
</header>
<table class="sub-header-table">
    <tr class="text-blue">
        <td>Kommission:</td>
        <td>Bestellnummer:</td>
        <td>Datum:</td>
        <td>Sachbearbeiter:</td>
    </tr>
    <tr>
        <td>
            <?php
            $parts = [];
            if (!empty($data['projectData']['internalProjectNo'])) {
                $parts[] = $data['projectData']['internalProjectNo'];
            }
            if (!empty($data['customerData']['searchName'])) {
                $parts[] = $data['customerData']['searchName'];
            }
            if (!empty($data['projectData']['commissionNumber'])) {
                $parts[] = $data['projectData']['commissionNumber'];
            }

            echo implode('-', $parts);
            ?>
        </td>
        <td><?= $data['artificialKey']; ?></td>
        <td><?= date('d.m.Y', strtotime($data['documentCreatedOn'])); ?></td>
        <td><?= $data['author']; ?></td>
    </tr>
</table>
<br>
<div>Bitte geben Sie auf allen Belegen unsere Kommission und unsere Bestellnummer an.</div>
<br>
<div>Sehr geehrte Damen und Herren, <br> hiermit bestellen wir folgende Materialien bzw. Leistungen</div>
<br>
<div>
    <span class="text-blue">Liefertermin:</span>
    <span>
        <?= isset($bestelllosData['Soll-Termin']) ? date('d.m.Y', strtotime($bestelllosData['Soll-Termin'])) : "" ?>
    </span>
</div>

<?php if (isset($bestelllosData['Lieferantenangebot'])) { ?>
    <div>
        <span class="text-blue">Ihr Angebot:</span>
        <span><?= $bestelllosData['Lieferantenangebot'] ?></span>
    </div>
<?php } ?>

<?php if (isset($data['positions']['Lieferort soll'])) { ?>
    <div>
        <span class="text-blue">Lieferort:</span>
        <span><?= displayLieferort($data) ?></span>
    </div>
<?php } ?>

<?php if (isset($data['positions']['Bemerkungen (extern)'])) { ?>
    <div>
        <span class="text-blue">Bemerkungen:</span>
        <span><?= $data['positions']['Bemerkungen (extern)'] ?></span>
    </div>
<?php } ?>

<br>
<table class="positions">
    <tr class="text-blue">
        <td>Position</td>
        <td>Titel</td>
        <td>Menge</td>
        <td>Anlage</td>
    </tr>
    <?php displayMainTable($data) ?>
</table>

<br>
<div class="text-left">Wir bitten um Auftrags- und Terminbestätigung</div>
<div class="text-left">Vielen Dank.</div>
<br>
<div>Freundliche Grüße</div>
<div><b>Gütersloher Bauelemente GmbH</b></div>

</body>
</html>