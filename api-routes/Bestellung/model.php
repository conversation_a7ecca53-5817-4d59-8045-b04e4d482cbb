<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Bestellung
{
    /**
     * @param string|int $schemaId
     * @param string|int $documentId
     * @return array<string, mixed>
     */
    public function getData($schemaId, $documentId): array
    {
        $menge = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $documentData = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl)['fullDocument'];
        $subsequentDocument = $this->getSubsequentDocument($documentData, $curl);
        if ($subsequentDocument) {
            $menge = $this->prepareMenge($documentData, $subsequentDocument['fullDocument']);
        }
        $positions = $this->mapChildrenUnderHeadlines($documentData['children'], $menge);
        $data['fileNames'] = $this->getFiles($positions, $curl);
        if ($positions['Bestellung'] ?? null) {
            $data['positions'] = array_merge($positions['Bestellung'], $positions);
        } else {
            $data['positions'] = $positions;
        }
        $data['documentCreatedOn'] = PrintoutHelper::dateConverter($documentData['documentCreatedOn'], 'd.m.Y');
        $data['artificialKey'] = $documentData['artificialKey'];
        $project = $this->getProjectData($documentData['documentRelKey1'], $curl);
        $data['projectData'] = $project;
        $data['customerData'] = $this->getCustomer($project['customerNo'], $curl);
        $data['author'] = $this->getAuthorName($documentData['author'], $curl);
        $data['supplierData'] = isset($data['positions']['Lieferant']) ? $this->getCustomer($data['positions']['Lieferant'], $curl) : [];

        return $data;
    }

    private function mapChildrenUnderHeadlines($children, $menge): array
    {
        $data = [];
        foreach ($children as $child) {
            if ($child['type'] == 'headline') {
                $data[$child['title']] = $this->preparePosition($child['id'], $children, $menge);
            }
        }
        return $data;
    }

    private function preparePosition($parentId, $children, $menge): array
    {
        $positions = [];
        foreach ($children as $child) {
            if (!isset($child['parentId'])) {
                continue;
            }

            if ($parentId == $child['parentId'] && $child['type'] != 'headline') {
                if ($child['title'] === 'Anhang') {
                    $positions[$child['title']] = $child['reportedValues'] ?? [];
                } else {
                    $positions[$child['title']] = $child['reportedValue'];
                }
                if (isset($child['linkedPositions'])) { //resolve menge value
                    foreach ($menge as $item) {
                        if ($item['id'] === $child['linkedPositions'][0]['documentationPositionId']) {
                            $positions['Menge'] = $item['menge'];
                        }
                    }
                }
            }
        }
        return $positions;
    }

    private function getProjectData($projectNo, $curl): array
    {
        $partial = 'internalProjectNo,customerNo,commissionNumber,projectSiteAddress,projectSiteZipCode,projectSiteCity';
        return PrintoutHelper::downloadProject($projectNo, $partial, $curl);
    }

    private function getAuthorName($pnr, $curl): string
    {
        return PrintoutHelper::downloadEmployee($pnr, $curl)['displayName'];
    }

    private function getCustomer($knr, $curl): array
    {
        if ($knr == "")
            return [];

        $knr = intval($knr);
        return PrintoutHelper::downloadCustomer($knr, $curl);
    }

    private function getSubsequentDocument($fullDocument, $curl): array
    {
        $document = [];
        foreach ($fullDocument['children'] as $child) {
            if (isset($child['linkedPositions'])) {
                $document = PrintoutHelper::downloadHierarchicalDocument(
                    $child['linkedPositions'][0]['schemaId'], $child['linkedPositions'][0]['documentId'], $curl, considerForPdfMerging: false);
                break;
            }
        }
        return $document;
    }

    private function prepareMenge($document, $subsequentDocument): array
    {
        $data = [];
        $id = [];
        foreach ($document['children'] as $child) {
            if (!isset($child['linkedPositions']))
                continue;
            $id[] = $child['linkedPositions'][0]['documentationPositionId'];
        }

        foreach ($subsequentDocument['children'] as $child) {
            if (in_array($child['id'], $id)) {
                $temp['id'] = $child['id'];
                $temp['parentId'] = $child['parentId'];
                $temp['menge'] = $this->getMengeReportedValue($child['parentId'], $subsequentDocument);
                $data[] = $temp;
            }
        }

        return $data;
    }

    private function getMengeReportedValue($parentId, $subsequentDocument): string
    {
        foreach ($subsequentDocument['children'] as $child) {
            if (!isset($child['parentId']))
                continue;
            if ($child['parentId'] == $parentId && $child['title'] === 'Menge') {
                return $child['reportedValue'];
            }
        }
        return '';
    }

    private function getFiles(array $positions, PrintoutCurl $curl): array
    {
        $data = [];
        foreach ($positions as $position) {
            if (empty($position['Anhang'])) {
                continue;
            }

            foreach ($position['Anhang'] as $fid) {
                if ($fid && !array_key_exists($fid, $data)) {
                    $download = PrintoutHelper::downloadFile($fid, $curl);
                    $description = $download['description'];
                    if (empty($description)) {
                        $description = $download['filename'];
                    }
                    $data[$fid] = $description;
                }
            }
        }
        return $data;
    }
}