<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Montagerapport())->getData($_GET['schemaId'], $_GET['documentId']);
}

function getChecks(array $data, string $title): string
{
    $checked = "<td style='width: 45px; font-size: 20px'>&#9745;</td>";
    $unchecked = "<td style='width: 45px; font-size: 20px'>&#9744;</td>";

    if (isset($data['mapped'][$title])) {
        if ($data['mapped'][$title] == 'Ja') {
            return $checked . $unchecked;
        }
        return $unchecked . $checked;
    }

    return $unchecked . $checked;
}

?>

<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Montagerapport</title>
</head>
<body>
<header>
    <div class="header-right">
        <img src="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("img/wohncenter.jpg") ?>" width="200px"
             height="auto" alt=""/>
    </div>
    <div class="header-left">
        <h2>Abnahmeprotokoll zur Lieferung
            <?php if ($data['mapped']['Datum']) {
                echo ' am ' . PrintoutHelper::dateConverter($data['mapped']['Datum'], 'd.m.Y');
            } ?>
        </h2>
        <p>Kunde: <?= $data['customer']['customerName'] ?>
            <span style="margin-left: 30px">
            Kaufvertrag: <?= $data['customer']['projectName'] ?>
            </span>
        </p>
    </div>
</header>

<main>
    <div class="border-container">
        <section class="flex-container">
            <table>
                <thead>
                <tr>
                    <th>Bei Möbellieferung / Montage</th>
                    <th>Ja</th>
                    <th>Nein</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td style="padding-left: 12px">Alle Teile sind vollständig und in Waage montiert.</td>
                    <?= getChecks($data, 'Alle Teile sind vollständig und in Waage montiert.'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Die Funktionen der beweglichen Teile wurden vorgeführt und erklärt.
                    </td>
                    <?= getChecks($data, 'Die Funktionen der beweglichen Teile wurden vorgeführt und erklärt.'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Die Oberflächen sind einwandfrei.</td>
                    <?= getChecks($data, 'Die Oberflächen sind einwandfrei.'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Die Ware wurde fachgerecht ausgerichtet</td>
                    <?= getChecks($data, 'Die Ware wurde fachgerecht ausgerichtet'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Die Polster / Bezüge sind in Ordnung</td>
                    <?= getChecks($data, 'Die Polster / Bezüge sind in Ordnung'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Die Elektroinstallation / Beleuchtung ist funktionstüchtig.</td>
                    <?= getChecks($data, 'Die Elektroinstallation / Beleuchtung ist funktionstüchtig.'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Wurden Fotos von der Montage gemacht?</td>
                    <?= getChecks($data, 'Wurden Fotos von der Montage gemacht?'); ?>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="3" style="padding-left: 12px"> FD-Nr.:
                        <span><?= $data['mapped']['FD-Nr'] ?? '' ?></span></td>
                </tr>
                <tr>
                    <td style="padding-left: 12px" colspan="3"> FD-Nr.:
                        <span><?= $data['mapped']['FD-Nr2'] ?? '' ?></span>
                    </td>
                </tr>
                <tr>
                    <td style="padding-left: 12px" colspan="3"> FD-Nr.:
                        <span><?= $data['mapped']['FD-Nr3'] ?? '' ?></span>
                    </td>
                </tr>
                <tr>
                    <td style="padding-left: 12px" colspan="3"> FD-Nr.:
                        <span><?= $data['mapped']['FD-Nr4'] ?? '' ?></span>
                    </td>
                </tr>
                <tr>
                    <td style="padding-left: 12px" colspan="3"> FD-Nr.:
                        <span><?= $data['mapped']['FD-Nr5'] ?? '' ?></span>
                    </td>
                </tr>
                </tbody>
            </table>
            <table>
                <thead>
                <tr>
                    <th>Zusätzlich bei Küchenmontage</th>
                    <th>Ja</th>
                    <th>Nein</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td style="padding-left: 12px">Einbaugeräte sind einwandfrei und wurden auf Funktion geprüft und
                        erklärt.
                    </td>
                    <?= getChecks($data, 'Einbaugeräte sind einwandfrei und wurden auf Funktion geprüft und erklärt.'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Wurden Fotos von der Montage gemacht?</td>
                    <?= getChecks($data, 'Wurden Fotos von der Montage gemacht?'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Schränke, Türen, Klappen und Schübe sind funktionstüchtig und sauber
                        ausgerichtet.
                    </td>
                    <?= getChecks($data, 'Schränke, Türen, Klappen und Schübe sind funktionstüchtig und sauber ausgerichtet.'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Die Wasserinstallation wurde auf Dichtigkeit geprüft.</td>
                    <?= getChecks($data, 'Die Wasserinstallation wurde auf Dichtigkeit geprüft.'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Pflegehinweise für die Arbeitsplatten wurden gegeben.</td>
                    <?= getChecks($data, 'Pflegehinweise für die Arbeitsplatten wurden gegeben.'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Auf die Feuchtigkeitsproblematik im Spülmaschinenbereich wurde
                        hingewiesen.
                    </td>
                    <?= getChecks($data, 'Auf die Feuchtigkeitsproblematik im Spülmaschinenbereich wurde hingewiesen.'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Die Betriebsanleitungen für die Geräte sind vorhanden.</td>
                    <?= getChecks($data, 'Die Betriebsanleitungen für die Geräte sind vorhanden.'); ?>
                </tr>
                <tr>
                    <td style="padding-left: 12px">Lässt sich der Geschirrspüler vollständig öffnen?</td>
                    <?= getChecks($data, 'Lässt sich der Geschirrspüler vollständig öffnen?'); ?>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td style="padding-left: 12px" colspan="3"> E-Nr.: <span><?= $data['mapped']['E-Nr'] ?? '' ?></span>
                    </td>
                </tr>
                <tr>
                    <td style="padding-left: 12px" colspan="3"> E-Nr.:
                        <span><?= $data['mapped']['E-Nr2'] ?? '' ?></span></td>
                </tr>
                <tr>
                    <td style="padding-left: 12px" colspan="3"> E-Nr.:
                        <span><?= $data['mapped']['E-Nr3'] ?? '' ?></span></td>
                </tr>
                <tr>
                    <td style="padding-left: 12px" colspan="3"> E-Nr.:
                        <span><?= $data['mapped']['E-Nr4'] ?? '' ?></span></td>
                </tr>
                <tr>
                    <td style="padding-left: 12px" colspan="3"> E-Nr.:
                        <span><?= $data['mapped']['E-Nr5'] ?? '' ?></span></td>
                </tr>
                </tbody>
            </table>
        </section>

        <section>
            <table class="sub-table">

                <tr>
                    <td>Wurde Kundeneigentum beschädigt (Wände, Böden, Einrichtung etc.)?</td>
                    <?= getChecks($data, 'Wurde Kundeneigentum beschädigt (Wände, Böden, Einrichtung etc.)?'); ?>
                </tr>
                <tr>
                    <td>Evtl. Fehlteile oder Beschädigungen wurden auf dem Lieferschein vermerkt</td>
                    <?= getChecks($data, 'Evtl. Fehlteile oder Beschädigungen wurden auf dem Lieferschein vermerkt'); ?>
                </tr>
                <tr>
                    <td>Die Verpackung wurde zurückgenommen</td>
                    <?= getChecks($data, 'Die Verpackung wurde zurückgenommen'); ?>
                </tr>
                <tr>
                    <td>Der Arbeitsplatz wurde sauber verlassen</td>
                    <?= getChecks($data, 'Der Arbeitsplatz wurde sauber verlassen'); ?>
                </tr>
                <tr>
                    <td>Der Fragebogen „Service-Check“ wurde übergeben</td>
                    <?= getChecks($data, 'Der Fragebogen „Service-Check“ wurde übergeben'); ?>
                </tr>
                <tr>
                    <td>Haben Sie Grund zur Beanstandung?</td>
                    <?= getChecks($data, 'Haben Sie Grund zur Beanstandung?'); ?>
                </tr>
            </table>
        </section>

        <section class="complaint-table">
            <table style="width: 100%">
                <tr>
                    <td>
                        <span class='text-underline'>Grund der Beanstandung:</span> <?= $data['mapped']['Grund der Beanstandung'] ?? '' ?>
                    </td>
                </tr>
            </table>
        </section>
    </div>
</main>

<section>
    <table class="remarks-table">
        <tr>
            <td><span class='text-underline'>Anmerkungen:</span> <?= $data['mapped']['Anmerkungen'] ?? '' ?></td>
        </tr>
    </table>
</section>

<section class="last-section">
    <table>
        <tr>
            <td><span class="text-underline">Monteur:</span> <?= $data['employeeName'] ?></td>
            <td class="text-underline">Unterschrift Monteur:</td>
            <td>
                <?php if (isset($data['mapped']['Unterschrift Monteur'])) { ?>
                    <img src="<?= $data['mapped']['Unterschrift Monteur'] ?>" alt="">
                <?php } ?>
            </td>
            <td class="text-underline">Unterschrift Kunde:</td>
            <td>
                <?php if (isset($data['mapped']['Unterschrift Kunde'])) { ?>
                    <img src="<?= $data['mapped']['Unterschrift Kunde'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
    </table>

    <table>
        <tr>
            <td><span class='text-underline'>Nachmontage:</span> <?= $data['mapped']['Nachmontage'] ?? '' ?></td>
            <td><span class="text-underline">Monteur:</span> <?= $data['employeeName'] ?></td>
            <td><span class="text-underline">Monteur:</span> <?= $data['employeeName'] ?></td>
            <td><span class="text-underline">Zeit:</span> <?= $data['mapped']['Zeit'] ?></td>
        </tr>
    </table>
</section>
</body>
</html>
