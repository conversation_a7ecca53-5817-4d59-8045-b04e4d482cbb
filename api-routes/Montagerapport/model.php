<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Montagerapport
{
    public function getData($schemaId, $documentId): array
    {
        $data = [];
        $curl = new PrintoutCurl();
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $schema = PrintoutHelper::downloadSchema($schemaId, $curl);

        $data['mapped'] = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], false, $schema);
        $data['employeeName'] = $this->getEmployeeDisplayName($doc['fullDocument'], 'Monteure', $curl);
        $data['customer'] = $this->getCustomerDetails($doc['fullDocument'], $curl);

        return $data;
    }

    public function getEmployeeDisplayName($fullDocument, $title, $curl): string
    {
        $employeeNo = -1;

        foreach ($fullDocument['children'] as $child) {
            if (!isset($child['title']))
                break;
            if ($child['title'] == $title) {
                $employeeNo = $child['reportedValue'];
                break;
            }
        }
        if ($employeeNo == -1)
            return '';
        $employee = PrintoutHelper::downloadEmployee($employeeNo, $curl);

        return $employee['displayName'];
    }

    public function getCustomerDetails($fullDocument, $curl): array
    {
        $data = [];
        $partial = "projectName,customerName";
        $project = PrintoutHelper::downloadProject($fullDocument['documentRelKey1'], $partial, $curl);

        $data['customerName'] = $project['customerName'];
        $data['projectName'] = $project['projectName'];

        return $data;
    }
}
