<?php
namespace GeruestFreigabe;

use C_GeruestFreigabe;
use PrintoutHelper;

require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_GeruestFreigabe())->getData($_GET['schemaId'] ?? '', $_GET['documentId'] ?? '');
}
$schemaKeys = $data['translatedSchemaKeys'];
$language = PrintoutHelper::getLanguage();

function renderCheckbox($data, $key, $value): void
{
    $checked = (($data[$key] ?? '') === $value) ? '&#9745;' : '&#9744;';
    echo "<span>$checked $value</span>";
}

function renderMultiCheckbox($data, $key, $values): void
{
    foreach ($values as $value) {
        $checked = in_array($value, $data[$key] ?? []) ? '&#9745;' : '&#9744;';
        echo "<span>$checked $value</span>";
    }
}

?>
<!DOCTYPE html>
<html lang="<?= $language ?>">
<head>
    <meta charset="UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title><?= ($language === 'en') ? 'Scaffold Approval' : 'Gerüstfreigabe' ?></title>
</head>
<body>
<div class="page">
    <!-- Header section -->
    <table class="header">
        <tr>
            <td class="logo" rowspan="2" style="text-align: center; width: 10%">
                <img src="<?= $data['companyLogo'] ?? '' ?>" alt="companyLogo">
            </td>
            <td class="client-title" rowspan="2" style="border-right: 1px solid; width: 50%">
                <?= $data['blankTitle'] ?? '' ?>
            </td>
            <td style="width: 10%">Nr.:</td>
            <td style="width: 30%"><?= $data['dokumentNr'] ?? '' ?></td>
        </tr>
        <tr>
            <td><?= PrintoutHelper::lang('customer') ?>:</td>
            <td><?= $data['schema']['customer'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2" rowspan="4" style="border-right: 1px solid; vertical-align: top">
                <b><?= $data['schema'][$schemaKeys['work_description']] ?? '' ?></b>
            </td>
            <td><?= PrintoutHelper::lang('location') ?>:</td>
            <td><?= $data['schema']['location'] ?? '' ?></td>
        </tr>
        <tr>
            <td><?= PrintoutHelper::lang('level') ?>:</td>
            <td><?= $data['schema'][$schemaKeys['level']] ?? '' ?></td>
        </tr>
        <tr>
            <td><?= PrintoutHelper::lang('coordinates') ?>:</td>
            <td><?= $data['schema'][$schemaKeys['coordinates']] ?? '' ?></td>
        </tr>
        <tr>
            <td><?= PrintoutHelper::lang('requester') ?>:</td>
            <td><?= $data['schema']['requester'] ?? '' ?></td>
        </tr>
    </table>

    <table class="mainContent">
        <tr class="row-color">
            <td colspan="2" style="width: 20%;"><b><?= PrintoutHelper::lang('execution_team') ?></b></td>
            <td style="width: 20%;"><b><?= ucfirst(PrintoutHelper::lang('signature')) ?></b></td>
            <td colspan="3" style="width: 40%;"><b><?= PrintoutHelper::lang('type_of_scaffold') ?></b></td>
        </tr>
        <tr>
            <td colspan="2" style="width: 20%">
                <?= $data[$schemaKeys['job_manager']] ?? '' ?>
            </td>
            <td style="width: 20%">
                <?php if (!empty($data['schema'][$schemaKeys['job_manager_signature']])) { ?>
                    <img src="<?= $data['schema'][$schemaKeys['job_manager_signature']] ?>"
                         alt="<?= PrintoutHelper::lang('signature') ?>" class="signature">
                <?php } ?>
            </td>
            <td style="width: 16%;">
                <?php renderMultiCheckbox($data['schema'], $schemaKeys['scaffold_type'], [$data['scaffoldTypeOptions'][0]]); ?>
            </td>
            <td style="width: 16%;">
                <?php renderMultiCheckbox($data['schema'], $schemaKeys['scaffold_type'], [$data['scaffoldTypeOptions'][2]]); ?>
            </td>
            <td style="width: 16%;">
                <?php renderMultiCheckbox($data['schema'], $schemaKeys['scaffold_type'], [$data['scaffoldTypeOptions'][4]]); ?>
            </td>
        </tr>
        <tr>
            <td colspan="2" style="width: 20%">
                <?= $data[$schemaKeys['job_executor']] ?? '' ?>
            </td>
            <td style="width: 20%">
                <?php if (!empty($data['schema'][$schemaKeys['job_executor_signature']])) { ?>
                    <img src="<?= $data['schema'][$schemaKeys['job_executor_signature']] ?>"
                         alt="<?= PrintoutHelper::lang('signature') ?>" class="signature">
                <?php } ?>
            </td>
            <!-- Second row of scaffold type checkboxes (3 options) -->
            <td style="width: 16%;">
                <?php renderMultiCheckbox($data['schema'], $schemaKeys['scaffold_type'], [$data['scaffoldTypeOptions'][1]]); ?>
            </td>
            <td style="width: 16%;">
                <?php renderMultiCheckbox($data['schema'], $schemaKeys['scaffold_type'], [$data['scaffoldTypeOptions'][3]]); ?>
            </td>
            <td style="width: 16%;">
                <?php renderMultiCheckbox($data['schema'], $schemaKeys['scaffold_type'], [$data['scaffoldTypeOptions'][5]]); ?>
            </td>
        </tr>
        <tr>
            <td class="textSize" colspan="3" rowspan="4" style="vertical-align: top; width: 60%">
                <u><b><?= PrintoutHelper::lang('safety_instructions') ?></b></u><br><br>
                - <?= PrintoutHelper::lang('change_scaffold_only_by_manufacturer') ?><br>
                - <?= PrintoutHelper::lang('allow_space_for_material_storage') ?><br>
                - <?= PrintoutHelper::lang('no_material_storage_on_protective_scaffolding') ?><br>
                - <?= PrintoutHelper::lang('do_not_overload_scaffolding_decks') ?><br>
                - <?= PrintoutHelper::lang('do_not_work_on_top_of_each_other') ?><br>
                - <?= PrintoutHelper::lang('use_existing_stairs_or_ladders') ?><br>
                - <?= PrintoutHelper::lang('keep_access_doors_closed') ?><br>
                - <?= PrintoutHelper::lang('do_not_jump_or_drop_material_on_scaffold_decks') ?><br>
                - <?= PrintoutHelper::lang('do_not_jeopardize_scaffold_stability') ?>
            </td>
            <td colspan="3" style="width: 40%">
                <table class="lastklasse-breitenklasse">
                    <tr>
                        <td colspan="4">
                            <b><?= PrintoutHelper::lang('load_class') ?></b>
                            (<?= PrintoutHelper::lang('uniformly_distributed_load') ?>)
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: left">
                            <?php renderCheckbox($data['schema'], $schemaKeys['load_class'], $data['loadClassValues'][0]); ?>
                        </td>
                        <td style="text-align: left">
                            <?php renderCheckbox($data['schema'], $schemaKeys['load_class'], $data['loadClassValues'][1]); ?>
                        </td>
                        <td style="text-align: left">
                            <?php renderCheckbox($data['schema'], $schemaKeys['load_class'], $data['loadClassValues'][2]); ?>
                        </td>
                        <td style="text-align: left">
                            <?php renderCheckbox($data['schema'], $schemaKeys['load_class'], $data['loadClassValues'][3]); ?>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <?php
                            renderCheckbox($data['schema'], $schemaKeys['width_class'], ($language === 'en') ? 'Other' : 'Sonstiges');
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4" style="padding-top: 15px"><b><?= PrintoutHelper::lang('width_class') ?>:</b>
                        </td>
                    </tr>
                    <tr>
                        <td><?php renderCheckbox($data['schema'], $schemaKeys['width_class'], $data['widthClassValues'][0]); ?></td>
                        <td><?php renderCheckbox($data['schema'], $schemaKeys['width_class'], $data['widthClassValues'][1]); ?></td>
                        <td colspan="2"><?php renderCheckbox($data['schema'], $schemaKeys['width_class'], $data['widthClassValues'][2]); ?></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td class="row-color" colspan="3" style="width: 40%">
                <b><?= PrintoutHelper::lang('approval_extension') ?></b>
            </td>
        </tr>
        <tr>
            <td style="vertical-align: bottom; width: 13%"><?= ucfirst(PrintoutHelper::lang('date')) ?></td>
            <td style="vertical-align: bottom; width: 13%"><?= ucfirst(PrintoutHelper::lang('name')) ?></td>
            <td style="vertical-align: bottom; width: 14%"><?= ucfirst(PrintoutHelper::lang('signature')) ?></td>
        </tr>
        <tr>
            <td style="vertical-align: bottom; width: 13%">
                <?= date('d.m.Y', strtotime($data['schema'][$schemaKeys['approval_date']] ?? '')) ?>
            </td>
            <td style="vertical-align: bottom; width: 13%">
                <?= $data['schema'][$schemaKeys['approval_name']] ?? '' ?>
            </td>
            <td style="text-align: center; width: 14%">
                <?php if (!empty($data['schema'][$schemaKeys['approval_signature']])) { ?>
                    <img src="<?= $data['schema'][$schemaKeys['approval_signature']] ?>"
                         alt="<?= PrintoutHelper::lang('signature') ?>" class="signature">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td class="row-color" colspan="3" style="width: 60%">
                <b><?= PrintoutHelper::lang('acceptance_by_scaffolding_users') ?></b>
            </td>
            <td style="width: 13%"><br></td>
            <td style="width: 13%"><br></td>
            <td style="width: 14%"><br></td>
        </tr>
        <tr>
            <td style="width: 20%; vertical-align: bottom">
                <?= date('d.m.Y', strtotime($data['schema'][$schemaKeys['user_acceptance_date']] ?? '')) ?>
            </td>
            <td style="width: 20%; vertical-align: bottom; font-size: 11px;">
                <?= $data['schema'][$schemaKeys['user_acceptance_name']] ?? '' ?>
            </td>
            <td style="width: 20%">
                <?php if (!empty($data['schema'][$schemaKeys['user_acceptance_signature']])) { ?>
                    <img src="<?= $data['schema'][$schemaKeys['user_acceptance_signature']] ?>"
                         alt="<?= PrintoutHelper::lang('signature') ?>" class="signature">
                <?php } ?>
            </td>
            <td style="width: 13%"><br></td>
            <td style="width: 13%"><br></td>
            <td style="width: 14%"><br></td>
        </tr>
    </table>

    <table class="row-color" style="width: 100%; border-collapse: collapse;">
        <tr>
            <td colspan="6" style="border: 1px solid lightgrey" class="eigenmahtige-text">
                <?= PrintoutHelper::lang('unauthorized_modifications_to_the_scaffolding_are_prohibited') ?><br>
            </td>
        </tr>
        <tr>
            <td colspan="6" style="border: 1px solid lightgrey" class="eigenmahtige-text2">
                <?= PrintoutHelper::lang('the_accident_prevention_regulations_and_legal_regulations_must_be_observed') ?>
            </td>
        </tr>
    </table>

    <br/>

    <table class="gerust-table">
        <tbody>
        <tr class="gerust-row-border">
            <td class="gerustGesperrt-title" colspan="9">!!!<?= PrintoutHelper::lang('scaffolding_closed') ?>!!!</td>
        </tr>
        <tr class="gerust-row-border cell-titles">
            <td colspan="3" style="width: 33%"><b><?= $data['dokumentNr'] ?? '' ?></b></td>
            <td colspan="3" style="width: 34%"><b><?= $data['projectData']['customerName'] ?? '' ?></b></td>
            <td colspan="3" style="width: 33%"><b><?= $data['schema'][$schemaKeys['location']] ?? '' ?></b></td>
        </tr>
        <tr>
            <td class="images-gerust borders" colspan="9" style="text-align: center">
                <img src="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("img/img1.jpg") ?>"
                     alt="imageSign1">
                <img src="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("img/img2.jpg") ?>"
                     alt="imageSign2">
                <img src="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("img/img3.jpg") ?>"
                     alt="imageSign3">
                <br>
            </td>
        </tr>
        </tbody>
    </table>

    <table class="textUnderImages" style="border: none;">
        <tr>
            <td style="width: 9%;"></td>
            <td class="row-color undersigns-text" style="vertical-align: center; text-align: center;">
                <?= PrintoutHelper::lang('attention') . ' - ' . PrintoutHelper::lang('do_not_use_the_scaffolding') ?>
            </td>
            <td style="width: 9%;"></td>
        </tr>
    </table>

    <table class="verboten_text">
        <tr>
            <td class="text-title"
                colspan="9"><?= PrintoutHelper::lang('any_unauthorized_use_of_the_scaffolding_is_prohibited') ?></td>
        </tr>
        <tr>
            <td style="width: 36%;"><b><?= PrintoutHelper::lang('reason_for_blocking') ?></b>:</td>
            <td colspan="3" style="width: 35%;"><b><?= PrintoutHelper::lang('detection_and_blocking_by') ?></b>:</td>
            <td colspan="3"><b><?= PrintoutHelper::lang('remedy_defects') ?></b>:</td>
        </tr>
        <tr>
            <td><b><?= PrintoutHelper::lang('short_description_of_the_defect') ?></b></td>
            <td style="text-align: center"><b><?= ucfirst(PrintoutHelper::lang('name')) ?></b></td>
            <td style="text-align: center"><b><?= ucfirst(PrintoutHelper::lang('date')) ?></b></td>
            <td style="text-align: center"><b><?= ucfirst(PrintoutHelper::lang('abbreviation')) ?></b></td>
            <td style="text-align: center"><b><?= ucfirst(PrintoutHelper::lang('name')) ?></b></td>
            <td style="text-align: center"><b><?= ucfirst(PrintoutHelper::lang('date')) ?></b></td>
            <td style="text-align: center"><b><?= ucfirst(PrintoutHelper::lang('abbreviation')) ?></b></td>
        </tr>
        <tr>
            <td><br></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td><br></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </table>
</div>
</body>
</html>
