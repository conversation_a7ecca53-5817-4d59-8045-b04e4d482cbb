<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Oversizes())->getData($_GET['billingServiceSheetNo'], $_GET['billingServiceSheetVersionNo']);
}

function formatGermanPhoneNumber($phoneNumber): string
{
    if (strlen($phoneNumber) >= 5) {
        $areaCode = substr($phoneNumber, 0, 5);
        $subscriberNumber = substr($phoneNumber, 5);
        return $areaCode . '/' . $subscriberNumber;
    }
    return $phoneNumber;
}

$address = $data['settings']['address'];
?>

<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Oversizes Header</title>

    <style>
        header {
            margin-left: 10mm;
            width: 277mm;
            font-family: Arial, 'sans-serif';
            font-size: 10px;
        }

        .text-align-right {
            text-align: right;
        }

        .bold-text {
            font-weight: bold;
        }

        .italic-text {
            font-style: italic;
        }

        table {
            width: 100%;
        }

        .header-table {
            border: 1px solid black;
            font-size: 12px;
        }

        .headings-table {
            width: 277mm;
            min-width: 277mm;
            max-width: 277mm;
            table-layout: fixed;
            border-bottom: 1px solid black;
        }

        .headings-table td {
            padding: 2px;
        }
    </style>
</head>
<header>
    <table class="address-info">
        <tr>
            <td class="bold-text"><?= $address['displayName'] ?></td>
            <td>
                <?php
                $parts = [];
                if ($address['phoneNumber']) {
                    $parts[] = 'Tel. ' . formatGermanPhoneNumber($address['phoneNumber']);
                }
                if ($address['faxNumber']) {
                    $parts[] = 'Fax ' . $address['faxNumber'];
                }
                if ($address['email']) {
                    $parts[] = $address['email'];
                }
                echo implode(', ', $parts);
                ?>
            </td>
        </tr>
    </table>

    <table class="header-table">
        <tr>
            <td width="7%" class="bold-text">Aufmaß</td>
            <td width="13%" class="italic-text"><?= displayDate($data['oversizes']['versionDate'] ?? '') ?></td>
            <td width="7%" class="bold-text">Kunde</td>
            <td width="43%"><?= $data['project']['customerNo'] . ' ' . $data['project']['customerName'] ?></td>
            <td width="20%">&nbsp;</td>
            <td width="10%" class="text-align-right"><span class="bold-text">Seite</span> <span
                        class="pageNumber italic-text"></span></td>
        </tr>
        <tr>
            <td class="bold-text">Nummer</td>
            <td><?= $data['oversizes']['compoundKey'] ?></td>
            <td class="bold-text">Projekt</td>
            <td><?= $data['project']['projectNo'] . ' ' . $data['project']['projectName'] ?></td>
            <td colspan="2" class="text-align-right"><span class="bold-text">Bewertung zum:</span>
                <span class="italic-text"><?= displayDate($data['oversizes']['evaluationDate'] ?? '') ?></span>
            </td>
        </tr>
    </table>

    <table class="headings-table">
        <tr>
            <td class="bold-text">Gerüst-Pos</td>
            <td></td>
            <td class="bold-text">Vorhaltung-Pos</td>
            <td class="bold-text">Kurztext</td>
            <td class="bold-text text-align-right">Stück</td>
            <td class="bold-text text-align-right">Länge</td>
            <td class="bold-text text-align-right">Tiefe</td>
            <td class="bold-text text-align-right">Höhe</td>
            <td class="bold-text text-align-right">Massen</td>
            <td class="bold-text">Vorhalte-beginn</td>
            <td class="bold-text">GVH bis (abw. Mietb)</td>
            <td class="bold-text">Bisherige Verlang.</td>
            <td class="bold-text">Vorhalte Ende.</td>
            <td class="bold-text text-align-right">Vorhalte Verlang.</td>
            <td class="bold-text text-align-right">Vorhalte Massen</td>
        </tr>
    </table>
</header>
</html>