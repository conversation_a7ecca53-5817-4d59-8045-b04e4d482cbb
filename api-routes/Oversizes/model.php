<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Oversizes
{
    private array $subItems = [];

    public function getData($billingServiceSheetNo, $billingServiceSheetVersionNo): array
    {
        $data = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());

        $oversizes = $this->downloadOversizes($billingServiceSheetNo, $billingServiceSheetVersionNo, $curl);
        $allOversizes = $this->downloadAllOversizes($billingServiceSheetNo, $billingServiceSheetVersionNo, $curl);

        $data['oversizeItems'] = [];
        foreach ($oversizes as $oversize) {
            if ($oversize['subItemsAvailableInformation'] > 0) {
                $id = $billingServiceSheetNo . '-' . $billingServiceSheetVersionNo . '-' . $oversize['billingServiceSheetItemId'];

                if (!isset($this->subItems[$id])) {
                    $this->subItems[$id] = $this->downloadSubItems(
                        $billingServiceSheetNo,
                        $billingServiceSheetVersionNo,
                        $oversize['billingServiceSheetItemId'],
                        $curl
                    );
                }

                $oversize['subItems'] = $this->subItems[$id];
            }
            $data['oversizeItems'][] = $oversize;
        }
        $data['oversizes'] = $allOversizes[0];
        $projectNo = $data['oversizes']['projectNo'];
        $data['project'] = PrintoutHelper::downloadProject($projectNo, 'customerNo,customerName,projectNo,projectName', $curl);
        $data['settings'] = PrintoutHelper::downloadInfoSettings($curl);

        return $data;
    }

    private function downloadAllOversizes($bssNo, $bssvNo, PrintoutCurl $curl): array
    {
        return json_decode($curl->_simple_call('get',
            "v1/oversizes/$bssNo/$bssvNo",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    private function downloadOversizes($bssNo, $bssvNo, PrintoutCurl $curl): array
    {
        return json_decode($curl->_simple_call('get',
            "v1/oversizes/items/$bssNo/$bssvNo",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    private function downloadSubItems($bssNo, $bssvNo, $bssiNo, PrintoutCurl $curl): array
    {
        return json_decode($curl->_simple_call('get',
            "v1/oversizes/subitems/$bssNo/$bssvNo/$bssiNo",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }
}