<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_GreenTag
{
    public function getData($projectNo, $internalNo): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $scaffold = $this->downloadScaffoldLists($projectNo, $internalNo, $curl);

        $data = empty($scaffold) ? [] : $scaffold[0];

        if (!empty($data['requesteeVirtualNo'])) {
            $partner = $this->getPartner($data['requesteeVirtualNo'], $curl);
            $name = empty($partner) ? '' : "{$partner[0]['firstName']} {$partner[0]['lastName']}";
            $data['partnerName'] = $name;
        }

        return $data;
    }

    private function downloadScaffoldLists(int $projectNo, int $internalNo, PrintoutCurl $curl): array
    {
        $url = "v1/scaffoldlists/select/$projectNo/$internalNo";
        $response = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($response, true) ?? [];
    }

    private function getPartner(int $virtualNo, PrintoutCurl $curl): array
    {
        $url = "v1/partners?virtualNo=$virtualNo";
        $response = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($response, true) ?? [];
    }
}