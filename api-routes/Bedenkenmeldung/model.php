<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';
require_once __DIR__ . '/../../utils/BundesInnungGeruestbauHelper.php';

class C_Bedenkenmeldung
{
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl();
        $doc = PrintoutHelper::downloadHierarchicalDocument((int)$schemaId, (int)$documentId, $curl);

        $mappedDocument = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument']);
        $data['Ausführungsart'] = $mappedDocument['die vorgesehene Ausführungsart'] ?? "";
        $data['Stoffe'] = $mappedDocument['die Güte der von Ihnen gelieferten Stoffe oder Bauteile'] ?? "";
        $data['Leistungen'] = $mappedDocument['die Leistungen anderer Unternehmer'] ?? "";
        $data['Pflicht'] = $mappedDocument['Hiermit genügen wir unserer Pflicht, gem. § 4 Abs. 3 VOB/B, wonach wir Ihnen unverzüglich Bedenken gegen die vorgesehene Art der Ausführung (auch wegen der Sicherung gegen Unfallgefahren), gegen die Güte der von Ihnen gelieferten Stoffe oder Bauteile oder gegen die Leistungen anderer Unternehmer mitzuteilen haben.'] ?? "";
        $data['Stellungnahme'] = $mappedDocument['Wir bitten Sie, unsere Bedenken unverzüglich zu überprüfen und hierzu Stellung zu nehmen.'] ?? "";
        $data['Ausführung'] = $mappedDocument['Wir sehen uns gezwungen, die Ausführung der vereinbarten Leistung vorerst nicht zu erbringen bzw. zu unterbrechen, bis uns eine Weisung Ihrerseits erreicht.'] ?? "";
        $data['FristCheckbox'] = $mappedDocument['Sollte uns bis zum... keine Stellungnahme von Ihnen vorliegen, gehen wir davon aus, dass der Vertrag entsprechend der ursprünglichen Vorgaben ausgeführt werden soll. Eine Haftung für eventuell dadurch später entstehende Unzulänglichkeiten lehnen wir jedoch schon jetzt ab.'] ?? "";
        $data['FristDatum'] = $mappedDocument['Datum eingeben...'] ?? "";
        $data['Begründung'] = $mappedDocument[', weil... (Begründung eingeben)'] ?? "";
        $data['Ort'] = $mappedDocument['Ort'] ?? "";
        $data['Datum'] = $mappedDocument['Datum'] ?? "";
        $data['Unterschrift des Auftragnehmers'] = $mappedDocument['Unterschrift des Auftragnehmers'] ?? "";
        $projectNo = $doc['fullDocument']['documentRelKey1'];
        return array_merge(BundesInnungGeruestbauHelper::downloadCommonDataForHeader($curl, $projectNo), $data);
    }
}