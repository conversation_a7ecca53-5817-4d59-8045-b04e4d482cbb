<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    try {
        $data = (new C_Bautagesbericht())->getData($_GET['schemaId'], $_GET['documentId']);
    } catch (Exception $e) {
        $data = [];
    }
}
$doc = $data['mappedChildren'];

function getStartStopTime($start, $end): string
{
    $startTime = empty($start) ? '' : $start;
    $endTime = empty($end) ? '' : $end;
    return "$startTime-$endTime";
}

function formatTemperature($temp): string
{
    if (empty($temp)) {
        return '';
    }
    return number_format($temp, 1, ',', '');
}

?>

<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Bautagesbericht</title>
</head>
<body>
<?php
if (isset($data['weather'])) {
    ?>
    <table class="weather-table">
        <tr>
            <td colspan="4" class="bg-gray">Witterung</td>
        </tr>
        <tr>
            <td rowspan="2">
                <img src="<?= $data['weather']['wmo']['image'] ?>"
                     alt="">
            </td>
            <td><?= $data['weather']['wmo']['description'] ?></td>
            <td>Temperatur:</td>
            <td>min. <?= formatTemperature($data['weather']['min'] ?? '') ?> °C</td>
        </tr>
        <tr>
            <td><?= $data['weather']['wmo']['rainInfo'] ?></td>
            <td>&nbsp;</td>
            <td>max. <?= formatTemperature($data['weather']['max'] ?? '') ?> °C</td>
        </tr>
    </table>
<?php } ?>

<table class="workers-table">
    <tr>
        <td class="text-align-center bg-gray">Arbeitskräfte</td>
    </tr>
</table>
<table class="workers-inner-table">
    <tr>
        <td>Nr.</td>
        <td>Qualifikation</td>
        <td>Arbeitszeit</td>
        <td>Pause</td>
    </tr>
    <?php if (isset($data['hours'])) {
        foreach ($data['hours'] as $hour) { ?>
            <tr>
                <td><?= $hour['pnr'] ?></td>
                <td><?= $hour['profession'] ?></td>
                <td><?= $hour['workDuration'] ?></td>
                <td><?= $hour['breakDuration'] ?></td>
            </tr>
        <?php } ?>
    <?php } else { ?>
        <tr>
            <td><?= $doc['Nr.'] ?? '' ?></td>
            <td><?= $doc['Qualifikation'] ?? '' ?></td>
            <td><?= getStartStopTime($doc['Arbeitszeit Start'] ?? '', $doc['Arbeitszeit Ende'] ?? '') ?></td>
            <td><?= getStartStopTime($doc['Pause Start'] ?? '', $doc['Pause Ende'] ?? '') ?></td>
        </tr>
        <tr>
            <td><?= $doc['Nr.2'] ?? '' ?></td>
            <td><?= $doc['Qualifikation2'] ?? '' ?></td>
            <td><?= getStartStopTime($doc['Arbeitszeit Start2'] ?? '', $doc['Arbeitszeit Ende2'] ?? '') ?></td>
            <td><?= getStartStopTime($doc['Pause Start2'] ?? '', $doc['Pause Ende2'] ?? '') ?></td>
        </tr>
        <tr>
            <td><?= $doc['Nr.3'] ?? '' ?></td>
            <td><?= $doc['Qualifikation3'] ?? '' ?></td>
            <td><?= getStartStopTime($doc['Arbeitszeit Start3'] ?? '', $doc['Arbeitszeit Ende3'] ?? '') ?></td>
            <td><?= getStartStopTime($doc['Pause Start3'] ?? '', $doc['Pause Ende3'] ?? '') ?></td>
        </tr>
        <tr>
            <td><?= $doc['Nr.4'] ?? '' ?></td>
            <td><?= $doc['Qualifikation4'] ?? '' ?></td>
            <td><?= getStartStopTime($doc['Arbeitszeit Start4'] ?? '', $doc['Arbeitszeit Ende4'] ?? '') ?></td>
            <td><?= getStartStopTime($doc['Pause Start4'] ?? '', $doc['Pause Ende4'] ?? '') ?></td>
        </tr>
        <tr>
            <td><?= $doc['Nr.5'] ?? '' ?></td>
            <td><?= $doc['Qualifikation5'] ?? '' ?></td>
            <td><?= getStartStopTime($doc['Arbeitszeit Start5'] ?? '', $doc['Arbeitszeit Ende5'] ?? '') ?></td>
            <td><?= getStartStopTime($doc['Pause Start5'] ?? '', $doc['Pause Ende5'] ?? '') ?></td>
        </tr>
    <?php } ?>
</table>

<table class="activities-table">
    <tr>
        <td class="text-align-center bg-gray">Tätigkeiten/Leistungsergebnisse</td>
    </tr>
    <tr>
        <td><?= $doc['Tätigkeiten/Leistungsergebnissee'] ?? '' ?></td>
    </tr>
</table>

<table class="comments-table">
    <tr>
        <td class="border text-align-center  bg-gray">Bemerkungen/Sonstiges</td>
    </tr>
    <tr>
        <td><?= $doc['Bemerkungen'] ?? '' ?></td>
    </tr>
</table>

<table class="photo-table">
    <tr>
        <td class="text-align-center border bg-gray">Fotodokumentation</td>
    </tr>
    <tr>
        <?php
        if (isset($doc['Fotodokumentation'])) {
            $photos = $doc['Fotodokumentation'];
            if (is_array($photos)) {
                foreach (array_chunk($photos, 3) as $row) {
                    foreach ($row as $photo) {
                        echo '<td><img src="' . $photo['filePath'] . '" alt=""></td>';
                    }
                }
            }
        }
        ?>
    </tr>
</table>

<section class="break-before">&nbsp;</section>

<table class="signatures-table">
    <tr>
        <td colspan="3" class="text-align-center border bg-gray">Unterschriften</td>
    </tr>
    <tr>
        <td class="text-align-center">Auftraggeber</td>
        <td class="text-align-center">Monteur</td>
        <td class="text-align-center">Bauleiter</td>
    </tr>
    <tr>
        <td class="border">
            <?php if (isset($doc['Auftraggeber'])): ?>
                <img src="<?= $doc['Auftraggeber']; ?>" alt="">
            <?php endif; ?>
        </td>
        <td class="border">
            <?php if (isset($doc['Monteur'])): ?>
                <img src="<?= $doc['Monteur']; ?>" alt="">
            <?php endif; ?>
        </td>
        <td class="border">
            <?php if (isset($doc['Bauleiter'])): ?>
                <img src="<?= $doc['Bauleiter']; ?>" alt="">
            <?php endif; ?>
        </td>
    </tr>
</table>
</body>
</html>
