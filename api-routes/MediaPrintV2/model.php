<?php
require_once __DIR__ . '/../MediaPrint/MediaPrintCommon.php';
require_once __DIR__ . '/../../vendor/autoload.php';

class C_MediaPrintV2 extends MediaPrintCommon
{
    /** @noinspection PhpUnusedParameterInspection ignore $saveHardCopy, it is only used in $config */
    public function getData(
        string|null $projectNo,
        // needs to be initialized to null because the MediaPrintV2/config.php does not specify it,
        // but the MediaWorkingOrderV2/config.php does
        string|null $workingOrderNo = null,
        string|null $saveHardCopy = null,
        string|null $hidePageNumbers = null,
        string|null $schemaId = null
    ): array
    {
        $data = $this->getDataCommon(projectNo: $projectNo, workingOrderNo: $workingOrderNo, schemaId: $schemaId);

        self::processPreviewQrVideoFields($data['files']);

        return array_merge($data, [
            'hidePageNumbers' => $hidePageNumbers === "true" || $hidePageNumbers === "1"
        ]);
    }
}