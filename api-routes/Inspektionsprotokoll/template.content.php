<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Inspektionsprotokoll())->getData(
        $_GET['schemaId'] ?? null,
        $_GET['documentId'] ?? null
    );
}

function renderProtocolRows(array $protocolData): string
{
    $output = '';
    foreach ($protocolData as $item) {
        switch ($item['type']) {
            case 'headline':
                $output .= '<tr>
                    <td colspan="3" class="font-weight-bold" style="padding: 6px 0 6px 3px">' . $item['title'] . '</td>
                </tr>';
                break;

            case 'headline_with_not_applicable':
                $checkbox = $item['notApplicableValue'] === "1" ? "&#9745;" : "&#9744;";
                $output .= '<tr>
                    <td colspan="3">
                        <table class="table-no-borders">
                            <tr>
                                <td class="font-weight-bold" style="padding: 6px 0 6px 3px">' . $item['title'] . '</td>
                                <td class="width-180">
                                    <div class="checkboxes">
                                        ' . $checkbox . '
                                        <span>nicht zutreffend</span>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>';
                break;

            case 'combobox':
                $yesCheckbox = $item['value'] ? "&#9745;" : "&#9744;";
                $noCheckbox = !$item['value'] ? "&#9745;" : "&#9744;";
                $output .= '<tr>
                    <td class="protocol-rows-td cell-num">' . $item['number'] . '</td>
                    <td class="protocol-rows-td thema-field">' . $item['title'] . '</td>
                    <td class="protocol-rows-td checkboxes">
                        ' . $yesCheckbox . '
                        <span>Ja</span>
                        ' . $noCheckbox . '
                        <span>Nein</span>
                    </td>
                </tr>';
                break;
        }
    }
    return $output;
}

function renderAdditionalRemarksRows(array $remarksData): string
{
    $output = '';
    foreach ($remarksData as $remark) {
        $output .= '<tr>
            <td colspan="2">' . $remark['toNumber'] . ' ' . $remark['remarkZuNr'] . '</td>
            <td colspan="6">' . $remark['remarkReportedValue'] . '</td>
            <td colspan="3">' . $remark['responsiblePerson'] . '</td>
            <td colspan="3">' . $remark['finishedBy'] . '</td>
        </tr>';
    }
    return $output;
}

function renderPhotoDocumentation(array $data): string
{
    $output = '';
    foreach ($data['photos'] ?? [] as $photoRow) {
        $output .= '<div>';
        foreach ($photoRow as $photo) {
            $output .= '<div class="photo-container">
                    <div class="image photo-image" style="background-image: url(' . $photo['url'] . ')"></div>
                    <div>
                        <span class="photo-caption">
                            Zu Nr. ' . $photo['number'] . '
                        </span>
                    </div>
                </div>';
        }
        $output .= '</div>';
    }
    return $output;
}

$projectName = $data["projectName"];
$author = $data["author"];
$documentDate = $data["documentDate"];
$protocolData = $data["protocolData"];
$additionalRemarksData = $data["additionalRemarksData"];
$photoDocumentation = renderPhotoDocumentation($data);
list($signature, $creator) = $data["signatures"];
$protocolRows = renderProtocolRows($protocolData);
$additionalRemarksRows = renderAdditionalRemarksRows($additionalRemarksData);
?>

<!DOCTYPE html>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Inspektionsprotokoll</title>
</head>
<body>
<table class="table-bordered">
    <tbody>
    <tr>
        <td class="threehpx project-author project-author-label">Baustelle:</td>
        <td class="project-author project-author-value"><?= $projectName ?></td>
    </tr>
    <tr>
        <td class="threehpx project-author project-author-label">Teilnehmer:</td>
        <td class="project-author project-author-value"><?= $author ?></td>
    </tr>
    </tbody>
</table>

<table class="main-table-container">
    <tbody>
    <tr>
        <td colspan="6" class="full-width-cell">
            <table class="protocol-table full-height-table">
                <tr>
                    <td colspan="2" class="center-title font-weight-bold">
                        Thema
                    </td>
                    <td class="center-title font-weight-bold min-width-150">
                        Mängel
                    </td>
                </tr>
                <?= $protocolRows ?>
            </table>
        </td>
    </tr>
    </tbody>
</table>
<table class="additional-remarks-table margin-top-60">
    <tr>
        <td colspan="2">
            Zu Nr.
        </td>
        <td colspan="6">
            Zusätzliche Bemerkungen bzw. Abweichung:
        </td>
        <td colspan="3">
            Zuständig:
        </td>
        <td colspan="3">
            Zu erledigen bis:
        </td>
    </tr>
    <?= $additionalRemarksRows ?>
</table>

<div class="photo-documentation-wrapper">
    <h2><b>Fotodokumentation:</b></h2>
    <?= $photoDocumentation ?>
</div>

<table>
    <tbody>
    <tr>
        <td>Datum: <?= $documentDate ?></td>
        <td>
            Ersteller:
            <?php if (!empty($creator)) { ?>
                <img class="signatures" src="<?= $creator ?>" alt="Signature">
            <?php } ?>
        </td>
        <td>
            Unterschrift:
            <?php if (!empty($signature)) { ?>
                <img class="signatures" src="<?= $signature ?>" alt="Signature">
            <?php } ?>
        </td>
    </tr>
    </tbody>
</table>
</body>
</html>
