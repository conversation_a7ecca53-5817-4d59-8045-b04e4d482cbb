<?php /** @noinspection HtmlDeprecatedAttribute */
namespace LuckeyCheckliste;

use C_LuckeyCheckliste;
use DateTime;
use PrintoutHelper;

require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_LuckeyCheckliste())->getData($_GET['schemaId'], $_GET['documentId']);
}
$partnerTitle1 = $data['partners'][0]['personalTitle'] ?? '';
$partnerName1 = $data['partners'][0]['firstName'] ?? '';
$partnerSurname1 = $data['partners'][0]['lastName'] ?? '';
$partnerTitle2 = $data['partners'][1]['personalTitle'] ?? '';
$partnerName2 = $data['partners'][1]['firstName'] ?? '';
$partnerSurname2 = $data['partners'][1]['lastName'] ?? '';

function renderCheckBox($title, $value, bool $isLast = false): void
{
    $lower = strtolower($value);
    $class = $isLast ? "" : " class='checkbox-row'";
    $padding = 'style="padding-top: 3px; padding-bottom: 3px"';
    if ($lower == 'ja' || (int)$value == 1 || $lower == 'true' || $lower == 'on') {
        $img = PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/checkbox-checked.png");
        echo
            "<tr $class>
            <td align='left' width='60%'>" . $title . '</td>' .
            "<td align='right' width='40%' $padding>" .
            '<img src="' . $img . '" alt="checked"  height="25px" width="auto"></td>';
    } else {
        $img = PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/checkbox-unchecked.png");
        echo
            "<tr $class>
            <td align='left' width='60%'>" . $title . '</td>' .
            "<td align='right' width='40%' $padding>" .
            '<img src="' . $img . '" alt="not checked"  height="25px" width="auto"></td>';
    }
}

function generateRow($title, $stringValue): void
{
    echo '<tr><td align="left" valign="top" width="60%">' . $title . '</td><td align="left" valign="top" width="40%">' . $stringValue . '</td></tr>';
}

/**
 * Checks if a file exists based on an image url. Returns true if the file exists and false if it doesn't.
 * @param string $url The url of the image
 * @return bool
 */
function UR_exists(string $url): bool
{
    $headers = get_headers($url);
    return (bool)stripos($headers[0], "200 OK");
}

function generateImage($title, $filePath): void
{
    if (UR_exists($filePath)) {
        echo '<tr><td align="left" valign="top" width="60%">' . $title .
            '</td><td align="right" valign="top" width="40%">' .
            '<img src="' . $filePath . '"' . ' alt="Foto" height="auto" width="75%">' .
            '</td></tr>';
    }
}

function isPositionTicked($positions): bool
{
    $results = array(true, 1, "1", "on");
    return count(array_intersect($positions, $results)) > 0;
}

?>

<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>LuckeyCheckliste</title>
    <!--suppress CssUnusedSymbol -->
    <style>
        body {
            font-family: Arial, sans-serif;
        }

        .projectData, .baustelle {
            width: 100%;
        }

        .projectData, .baustelle {
            font-size: 17px;
        }

        .underlined {
            border-bottom: 2px solid black;
        }

        .headerDiv {
            background-color: lightgrey;
            padding: 5px;
        }

        ul {
            margin-left: 10px;
            padding: 10px;
        }

        .smallFont {
            font-size: 11px;
        }

        .noBullet {
            list-style: none;
        }

        .checkboxesCombobox, .images, .signatures, .weather {
            width: 100%;
            border-collapse: collapse;
        }

        .title {
            font-size: large;
        }

        .subtitle {
            font-size: 17px;
            padding-top: 9px;
            color: #5c5c5c;
            font-weight: bold;
        }

        .checkbox-row {
            border-bottom: 1px solid gray;
        }

        .underlinedTop {
            border-top: 2px solid black;

            .pageBreak {
                page-break-inside: avoid;
                position: absolute;
            }

            @media print {
                .pageBreak {
                    page-break-inside: avoid;
                }
            }
    </style>

</head>
<body>

<table class="projectData">
    <tr>
        <td width="20%" align="left"><b>Datum:</b></td>
        <td width="30%" align="left">
            <?php
            $startTime = strtotime($data['woOrProjectData']['startDate']);
            echo sprintf("%s, %s",
                $data['days'][strtolower(date('l', $startTime))],
                date('d.m.Y', $startTime));
            ?>
        </td>
    </tr>
    <tr>
        <td align="left" valign="top"><b>Monteur:</b></td>
        <td><?= $data['teamMembers']; ?></td>
    </tr>
    <tr>
        <td align="left" valign="top"><b>Projekt:</b></td>
        <td align="left"><?= $data['projectName']; ?></td>
    </tr>
    <tr>
        <td align="left"><b>Bauleiter:</b></td>
        <td align="left"><?= $data['projectManager']; ?></td>
    </tr>
</table>

<br>
<div class="underlined"></div>

<table class="baustelle">
    <tr>
        <td colspan="4"><?= str_repeat('&nbsp;', 1); ?></td>
    </tr>
    <tr>
        <td width="20%" align="left" valign="top"><b>Baustelle:</b></td>
        <td width="30%" align="left" valign="top">
            <b><?= $data['projectAddress'] ?? ''; ?></b>
        </td>
        <td width="20%" valign="top" align="right"><b>Auftraggeber:</b></td>
        <td width="30%" align="left" valign="top">
            <b><?= $data['customerName'] ?></b><br>
            <b><?= $data['customerAddress'] ?></b>
        </td>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <tr>
        <td align="left" valign="top"><b>Ansprechpartner:</b></td>
        <td width="30%" align="left"
            valign="top"><?= $partnerTitle1 . ' ' . $partnerName1 . ' ' . $partnerSurname1; ?></td>
        <td width="20%" valign="top" align="right"></td>
        <td width="30%" align="left"
            valign="top"><?= $partnerTitle2 . ' ' . $partnerName2 . ' ' . $partnerSurname2; ?></td>
    </tr>
    <tr>
        <td align="left" valign="top"><b>Rufnummer:</b></td>
        <td width="30%" align="left" valign="top"><?= $data['partners'][0]['phone'] ?? ''; ?></td>
        <td width="20%" valign="top" align="right"></td>
        <td width="30%" align="left" valign="top"><?= $data['partners'][1]['phone'] ?? ''; ?></td>
    </tr>
    <tr>
        <td align="left" valign="top"><b>Mobil:</b></td>
        <td width="30%" align="left" valign="top"><?= $data['partners'][0]['cell'] ?? ''; ?></td>
        <td width="20%" valign="top" align="right"></td>
        <td width="30%" align="left" valign="top"><?= $data['partners'][1]['cell'] ?? ''; ?></td>
    </tr>
</table>

<br>
<!--end luckey reusable data -->

<?php if (!empty($data['woOrProjectData']['langtext'])) { ?>
    <table class="checkboxesCombobox">
        <tr class="underlinedTop">
            <td colspan="2"><?= str_repeat('&nbsp;', 1); ?></td>
        </tr>
        <tr>
            <td align="left" colspan="2" class="title"><b>Auftragsbeschreibung</b></td>
        </tr>
        <tr>
            <td><?= nl2br(trim($data['woOrProjectData']['langtext'])); ?></td
        </tr>
    </table>
<?php } ?>

<?php if (!empty($data['document']['Wetter'])) { ?>
    <table class="weather pageBreak" style="margin-top: 20px">
        <tr>
            <td align="left" colspan="2" class="title"><b>Wetter</b></td>
        </tr>
        <?php
        generateRow('Temperatur (in °C)', $data['document']['Wetter']['Temperatur (in °C)'] ?? '');
        generateRow('Verhältnis', $data['document']['Wetter']['Verhältnis'] ?? '');
        generateRow('Wetterbedingte Einschränkung', $data['document']['Wetter']['wetterbedingte Einschränkung'] ?? '');
        ?>
    </table>
<?php } ?>

<?php
$hasFassadeLeistungsstand = !empty($data['document']['Fassade / Fenster / Türen']['Leistungsstand zur Auftragsbeschreibung']);
$hasFassadeArbeiten = isPositionTicked($data['document']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten'] ?? []);
if ($hasFassadeLeistungsstand || $hasFassadeArbeiten) { ?>
    <table class="checkboxesCombobox" style="margin-top: 20px">
        <tr>
            <td colspan="2" align="left" class="title"><b>Fassade / Fenster / Türen</b></td>
        </tr>
        <?php if ($hasFassadeLeistungsstand) {
            generateRow('Leistungsstand zur Auftragsbeschreibung',
                $data['document']['Fassade / Fenster / Türen']['Leistungsstand zur Auftragsbeschreibung']);
        }
        if ($hasFassadeArbeiten) { ?>
            <tr>
                <td colspan="2" align="left" class="subtitle">Ausgeführte Arbeiten</td>
            </tr>
            <?php
            renderCheckbox('Gestellt und ausgerichtet', $data['document']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Gestellt und ausgerichtet'] ?? '');
            renderCheckbox('Befestigt', $data['document']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Befestigt'] ?? '');
            renderCheckbox('Automatiktürbereich (Verkabelung, Leibungsbleche, Schwelle)',
                $data['document']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Automatiktürbereich (Verkabelung, Leibungsbleche, Schwelle)'] ?? '');
            renderCheckbox('Verkabelung (falls erforderlich)', $data['document']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Verkabelung (falls erforderlich)'] ?? '');
            renderCheckbox('Anschlüsse', $data['document']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Anschlüsse'] ?? '');
            renderCheckbox('Verglasung', $data['document']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Verglasung'] ?? '');
            renderCheckbox('Dichtungen', $data['document']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Dichtungen'] ?? '');
            renderCheckbox('Druck- u. Deckschalen', $data['document']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Druck- u. Deckschalen'] ?? '',
                isLast: true);
        } ?>
    </table>
<?php } ?>

<?php
$hasWintergartenLeistungsstand = !empty($data['document']['Wintergarten / Überdachung']['Leistungsstand zur Auftragsbeschreibung']);
$hasWintergartenArbeiten = isPositionTicked($data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten'] ?? []);
if ($hasWintergartenLeistungsstand || $hasWintergartenArbeiten) { ?>
    <table class="checkboxesCombobox" style="margin-top: 20px">
        <tr>
            <td colspan="2" align="left" class="title"><b>Wintergarten / Überdachung</b></td>
        </tr>
        <?php if ($hasWintergartenLeistungsstand) {
            generateRow('Leistungsstand zur Auftragsbeschreibung',
                $data['document']['Wintergarten / Überdachung']['Leistungsstand zur Auftragsbeschreibung']);
        }
        if ($hasWintergartenArbeiten) { ?>
            <tr>
                <td colspan="2" align="left" class="subtitle">Ausgeführte Arbeiten</td>
            </tr>
            <?php
            renderCheckbox('Befestigt', $data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Wandanschluss'] ?? '');
            renderCheckbox('Stützen, Rinnenprofil', $data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Stützen, Rinnenprofil'] ?? '');
            renderCheckbox('Dachsparren', $data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Dachsparren'] ?? '');
            renderCheckbox('Dach verglast', $data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Dach verglast'] ?? '');
            renderCheckbox('Anschlüsse', $data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Anschlüsse'] ?? '');
            renderCheckbox('Dach abgedichtet', $data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Dach abgedichtet'] ?? '');
            renderCheckbox('Elemente montiert', $data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Elemente montiert'] ?? '');
            renderCheckbox('Elemente verglast', $data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Elemente verglast'] ?? '');
            renderCheckbox('Fußpunkt abgeklebt / gedämmt', $data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Fußpunkt abgeklebt / gedämmt'] ?? '');
            renderCheckbox('Bleche / Kantungen angebracht',
                $data['document']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Bleche / Kantungen angebracht'] ?? '',
                isLast: true);
        } ?>
    </table>
<?php } ?>

<?php
$hasMarkiseLeistungsstand = !empty($data['document']['Markise / Beschattung']['Leistungsstand zur Auftragsbeschreibung']);
$hasMarkiseArbeiten = isPositionTicked($data['document']['Markise / Beschattung']['Ausgeführte Arbeiten'] ?? []);
if ($hasMarkiseLeistungsstand || $hasMarkiseArbeiten) {
    ?>
    <table class="checkboxesCombobox" style="margin-top: 20px">
        <tr>
            <td colspan="2" align="left" class="title"><b>Markise / Beschattung</b></td>
        </tr>
        <?php if ($hasMarkiseLeistungsstand) {
            generateRow('Leistungsstand zur Auftragsbeschreibung',
                $data['document']['Markise / Beschattung']['Leistungsstand zur Auftragsbeschreibung']);
        }
        if ($hasMarkiseArbeiten) { ?>
            <tr>
                <td colspan="2" align="left" class="subtitle">Ausgeführte Arbeiten</td>
            </tr>
            <?php
            renderCheckbox('Beschattung montiert', $data['document']['Markise / Beschattung']['Ausgeführte Arbeiten']['Beschattung montiert'] ?? '');
            renderCheckbox('Beschattung eingelernt', $data['document']['Markise / Beschattung']['Ausgeführte Arbeiten']['Beschattung eingelernt'] ?? '',
                isLast: true);
        } ?>
    </table>
<?php } ?>

<?php if (!empty($data['document']['Glasgestelle freimelden'])) { ?>
    <div style="margin-top: 20px">
        <b>Glasgestelle freimelden: </b><?= $data['document']['Glasgestelle freimelden']; ?>
    </div>
<?php } ?>

<?php if (!empty($data['document']['Bemerkungen / Hinweise'])) { ?>
    <div style="margin-top: 20px">
        <b>Bemerkungen / Ergebnisse: </b><?= nl2br(trim($data['document']['Bemerkungen / Hinweise'])); ?>
    </div>
<?php } ?>

<?php if (!empty($data['document']['Fotos']['Fotos'])) { ?>
    <table class="images pageBreak" style="margin-top: 20px">
        <?php generateImage('Fotos', $data['document']['Fotos']['Fotos']); ?>
    </table>
<?php } ?>

<br>
<br>
Die Arbeiten sind vollständig und vertragsgemäß erbracht worden.

<?php if (!empty($data['document']['Abnahme'])) { ?>
    <br><br><br>
    <table class="signatures pageBreak" cellspacing="0">
        <tr>
            <th width="22%" align="left" valign="top" class="highlight">
                <?php
                /** @noinspection PhpUnhandledExceptionInspection */
                $dateTime = new DateTime($data['document']['Abnahme']['Einsatzdatum'] ?: $data['woOrProjectData']['startDate']);
                echo $dateTime->format('d.m.Y');
                ?>
            </th>
            <th width="4%"></th>
            <th width="33%" align="left" valign="top">
                <?php if (isset($data['document']['Abnahme']['Unterschrift Auftraggeber / Vertreter'])) { ?>
                    <img src="<?= $data['document']['Abnahme']['Unterschrift Auftraggeber / Vertreter'] ?>"
                         alt="signature" class="logo" width="75%" height="auto">
                <?php } ?>
            </th>
            <th width="4%"></th>
            <th width="33%" align="left" valign="top">
                <?php if (isset($data['document']['Abnahme']['Unterschrift Monteur']) &&
                    UR_exists($data['document']['Abnahme']['Unterschrift Monteur'])) { ?>
                    <img src="<?= $data['document']['Abnahme']['Unterschrift Monteur'] ?>" alt="signature"
                         class="logo" width="75%" height="auto">
                <?php } ?>
            </th>
            <th width="4%"></th>
        </tr>
        <tr>
            <td class="smallFont highlight" align="left" valign="top"><b>Datum</b></td>
            <td></td>
            <td class="smallFont highlight" align="left" valign="top"><b>Unterschrift Auftraggeber / Vertreter</b></td>
            <td></td>
            <td class="smallFont highlight" align="left" valign="top"><b>Unterschrift Monteur</b></td>
        </tr>
        <tr>
            <td class="smallFont" align="left" valign="top"></td>
            <td></td>
            <td class="smallFont" align="left" valign="top">
                <b><?= '(' . $data['document']['Abnahme']['Name Auftraggeber / Vertreter'] . ')'; ?></b></td>
            <td></td>
            <td class="smallFont" align="left" valign="top">
                <b><?= '(' . $data['document']['Abnahme']['Name Monteur'] . ')'; ?></b></td>
        </tr>
    </table>
<?php } ?>
</body>
</html>