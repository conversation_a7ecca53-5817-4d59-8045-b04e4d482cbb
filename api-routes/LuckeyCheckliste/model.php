<?php /** @noinspection DuplicatedCode */

require_once __DIR__ . '/../../printout.helper.php';

class C_LuckeyCheckliste
{
    private PrintoutCurl $curl;

    public function __construct()
    {
        $this->curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
    }

    public function getData($schemaId, $documentId): array
    {
        $principal = PrintoutHelper::getPrincipal();
        $documentData = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $this->curl)['fullDocument'];

        $main['id'] = $documentData['id'];
        $main['title'] = $documentData['title'];
        $main['type'] = $documentData['type'];
        $main['required'] = $documentData['required'];
        $main['reportedValues'] = [];

        array_unshift($documentData['children'], $main);

        $grouped = PrintoutHelper::groupChildrenUnderParentRecursive($documentData['children'], 'id', 'parentId');

        $data = $this->getDeepValueFromField('title', 'reportedValue', $grouped[0], 'children');
        $isDataEmpty = $data === null;
        $projectData = $this->downloadProjectData($documentData['documentRelKey1']);

        $isLuckeyOnline = ($principal === 'luckeyonline');
        if (!empty($documentData['documentRelKey2'])) {
            $woOrProjectData = $this->downloadWorkingOrder($documentData['documentRelKey1'], $documentData['documentRelKey2']);
            if ($isLuckeyOnline) {
                $woOrProjectData['startDate'] = $woOrProjectData['plan_date'];
            } else {
                $woOrProjectData['startDate'] = $woOrProjectData['date'];
            }
        } else {
            $woOrProjectData = $this->downloadProjectData($documentData['documentRelKey1']);
        }
        $data['teamMembers'] = $this->downloadEmployeeNames($woOrProjectData['staff_preplanned']);
        $data['woOrProjectData'] = $woOrProjectData;

        $customerData = [];
        if (!empty($projectData)) {
            $customerData = PrintoutHelper::downloadCustomer($projectData['knr'], $this->curl);
        }

        if ($customerData) {
            if ($isLuckeyOnline) {
                $customer = $customerData;
            } else {
                if ($customerData['id'] ?? null) {
                    $customer = $customerData;
                } else {
                    $customer = $customerData[0];
                }
            }
            if (isset($customer['name'])) {
                $data['customerName'] = $customer['name'];
            } else {
                $data['customerName'] = '';
            }

            $data['customerAddress'] = '';
            if (isset($customer['address'])) {
                $data['customerAddress'] .= $customer['address'] . ", ";
            }
            if (isset($customer['postCode'])) {
                $data['customerAddress'] .= $customer['postCode'] . ' ';
            }

            $data['customerAddress'] .= $customer['city'] ?? '';
            $data['customerAddress'] = trim($data['customerAddress']);
        }

        if ($projectData) {
            $data['projectName'] = $projectData['project_name'];
            $data['projectDate'] = date('d.m.Y', strtotime($projectData['start_date']));
            $data['projectNumber'] = $projectData['ktr'];

            if (isset($projectData['technicalContactDisplayName'])
                && !is_array($projectData['technicalContactDisplayName'])
                && $projectData['technicalContactDisplayName']) {

                $data['projectManager'] = $projectData['technicalContactDisplayName'];
            } else {
                if (empty($projectData['technicalContactKey'])) {
                    $data['projectManager'] = '';
                } else {
                    $employee = PrintoutHelper::downloadEmployee((int)$projectData['technicalContactKey'], $this->curl);
                    $data['projectManager'] = $employee['displayName'] ?? '';
                }
            }

            $data['projectAddress'] = $projectData['customer_address'] ?: '';
        }

        $data['partners'] = $this->downloadPartners($documentData['documentRelKey1']);
        $data['logo'] = PrintoutHelper::downloadSettings($this->curl)['logo'];
        $data['schemaTitle'] = $documentData['title'];

        // use the German names of the days, but not changing the locale settings
        $data['days'] = [
            'monday' => 'Montag',
            'tuesday' => 'Dienstag',
            'wednesday' => 'Mittwoch',
            'thursday' => 'Donnerstag',
            'friday' => 'Freitag',
            'saturday' => 'Samstag',
            'sunday' => 'Sonntag'
        ];

        if ($isDataEmpty) {
            // ignore, this happens on a completely empty document
        } else if ($data['Bautagesbericht'] ?? null) {
            $data['document'] = $data['Bautagesbericht'];
            unset($data['Bautagesbericht']);
        } else if ($data['Bautagesbericht v2'] ?? null) {
            $data['document'] = $data['Bautagesbericht v2'];
            unset($data['Bautagesbericht v2']);
        } else {
            die_with_response_code(Response::BAD_REQUEST,
                "Unknown document type. Expected either \"Bautagesbericht\" or \"Bautagesbericht v2\". \$data is "
                . json_encode($data, JSON_PRETTY_PRINT));
        }

        return $data;
    }

    private function getDeepValueFromField($keyFieldName, $valueFieldName, $node, $childrenFieldName): ?array
    {
        $paths = [];
        if (!empty($node[$valueFieldName])) {
            return [$node[$keyFieldName] => $node[$valueFieldName]];
        } elseif (!empty($node[$childrenFieldName]) && is_array($node[$childrenFieldName])) {
            foreach ($node[$childrenFieldName] as $child) {
                if (is_array($child) && isset($node[$keyFieldName], $child[$keyFieldName])) {
                    $childPath = $this->getDeepValueFromField(
                        $keyFieldName,
                        $valueFieldName,
                        $this->setPathAsValue($child),
                        $childrenFieldName
                    );
                    if (isset($childPath[$child[$keyFieldName]])) {
                        $paths[$node[$keyFieldName]][$child[$keyFieldName]] = $childPath[$child[$keyFieldName]];
                    }
                }
            }
            return $paths;
        }
        return null;
    }

    private function setPathAsValue($child)
    {
        if (isset($child['filePath'])) {
            $child['reportedValue'] = $child['filePath'];
        }
        return $child;
    }

    private function downloadProjectData($projectNo)
    {
        $year = date('Y');
        return json_decode($this->curl->_simple_call('get', "v1/projects/select/$projectNo/$year",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    private function downloadEmployeeNames(array $staffPreplanned): string
    {
        $personalNumbers = implode(',', $staffPreplanned);
        // returns either an array or a single employee when $personalNumbers is a single number
        $employees = json_decode($this->curl->_simple_call('get', "v3/employees?employeeNumbers=$personalNumbers",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
        if (array_key_exists("displayName", $employees)) {
            return trim($employees['displayName']);
        }
        $employeeNames = array_map(
            fn($employee) => trim($employee['displayName']),
            $employees);
        return implode(", ", $employeeNames);
    }

    private function downloadWorkingOrder($ktr, $aanr)
    {
        return json_decode($this->curl->_simple_call('get', "v1/workingorders/select/$ktr/$aanr",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    private function downloadPartners($ktr)
    {
        return json_decode($this->curl->_simple_call('get', "v1/partners/select/$ktr",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }
}
