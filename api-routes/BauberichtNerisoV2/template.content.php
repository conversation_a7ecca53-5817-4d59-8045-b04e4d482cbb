<?php /** @noinspection HtmlUnknownTarget */
require_once __DIR__ . '/../../printout.helper.php';

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_BauberichtNerisoV2())->getData($_GET['schemaId'], $_GET['documentId']);
}

function displayImage(array $data): void
{
    $images = [];
    if (isset($data['Foto'])) {
        $images = $data['Foto'];
    } else if (isset($data['Fotos'])) {
        $images = $data['Fotos'];
    }
    if (empty($images)) {
        return;
    }

    echo "<div class='break-before'></div>";
    $imageCounter = 0;

    foreach ($images as $image) {
        /** @noinspection HtmlUnknownTarget */
        echo sprintf("<img class='img' src='%s' alt='photo'>", $image['filePath']);
        $imageCounter++;

        if ($imageCounter == 2) {
            echo "<div class='break-before'></div>";
            $imageCounter = 0;
        }
    }
}

?>

<!DOCTYPE html>
<html lang="de">
<head>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Baubericht Neriso</title>
</head>

<body style="padding-top: 15px">

<?php
$weatherDetails = [];
if (isset($data['Temperatur (in °C)'])) {
    $weatherDetails[] = $data['Temperatur (in °C)'] . "°C";
}
if (isset($data['Verhältnis'])) {
    $weatherDetails[] = $data['Verhältnis'];
}
if (!empty($weatherDetails)) {
    echo "<b>Wetter: " . implode("&nbsp;", $weatherDetails) . "</b><br>";
}
?>

<?php if ($data['hasWetterbedingteEinschränkungen']) { ?>
    <b>Wetterbedingte Einschränkungen: <?= $data['Wetterbedingte Einschränkungen:'] ?? 'keine'; ?></b>
    <br>
<?php } ?>
<div class="border-bottom" style="padding-top: 9px"></div>

<?php
$anwesende = null;
if (isset($data['Anwesende Firmen / Personen'])) {
    $anwesende = $data['Anwesende Firmen / Personen'];
} else if (isset($data['Anwesende'])) {
    $anwesende = $data['Anwesende'];
}
?>
<?php if ($anwesende) { ?>
    <br>
    <b>Anwesende Firmen/Personen:</b>
    <br>
    <?= nl2br($anwesende); ?>
<?php } ?>

<?php
$feststellungenLeistungsstand = null;
if (isset($data['Feststellungen / Leistungsstand'])) {
    $feststellungenLeistungsstand = $data['Feststellungen / Leistungsstand'];
} else {
    $parts = [];
    if (isset($data['Leistungsstand'])) {
        $parts[] = $data['Leistungsstand'];
    }
    if (isset($data['Feststellungen'])) {
        $parts[] = $data['Feststellungen'];
    }
    if (!empty($parts)) {
        $feststellungenLeistungsstand = implode("\n", $parts);
    }
}
if ($feststellungenLeistungsstand) { ?>
    <br><br>
    <b>Feststellungen/Leistungsstand: </b>
    <br>
    <?= nl2br($feststellungenLeistungsstand); ?>
<?php } ?>

<?php if ($data['Sonstiges'] ?? null) { ?>
    <br><br>
    <b>Sonstiges:</b>
    <br>
    <?= $data['Sonstiges'] ?>
<?php } ?>

<div class='main-table'><?php displayImage($data); ?></div>

<br>
<div class="border-bottom"></div>
<br>

Datum: <?= $data['dateOfVisit'] ?? ''; ?>
<br>
<br>
<b>Unterschriften:</b>

<table class="signatures" style="margin-top: 21px; margin-left: -2px;">
    <tr>
        <td>
            Auftraggeber / Vertreter: <?= $data['Name Bauherr'] ?? ''; ?>
        </td>
        <td>
            Handwerker: <?= $data['Name Handwerker'] ?? ''; ?>
        </td>
        <td>
            Bauleiter: <?= $data['Name Bauleiter'] ?? ''; ?>
        </td>
    </tr>
    <tr>
        <td>
            <?php
            if (isset($data['Unterschrift Bauherr'])) {
                echo sprintf('<img src="%s" alt="">', $data['Unterschrift Bauherr']);
            }
            ?>
        </td>
        <td>
            <?php
            if (isset($data['Unterschrift Handwerker'])) {
                echo sprintf('<img src="%s" alt="">', $data['Unterschrift Handwerker']);
            }
            ?>
        </td>
        <td>
            <?php
            if (isset($data['Unterschrift Bauleiter'])) {
                echo sprintf('<img src="%s" alt="">', $data['Unterschrift Bauleiter']);
            }
            ?>
        </td>
    </tr>
</table>

</body>
</html>
