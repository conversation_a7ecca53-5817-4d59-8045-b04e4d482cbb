<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';

class C_Kalkulationsdatenblatt
{
    public function getData($schemaId, $documentId): array
    {
        $curl = new PrintoutCurl();
        $schemaChildren = PrintoutHelper::downloadSchema($schemaId, $curl)['children'];
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $mapped = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], true, $schemaChildren);
        $displayInsideChildren = $this->getDisplayInside($doc['fullDocument']);
        $mapped['temps']['documentCreatedOn'] = PrintoutHelper::dateConverter($doc['fullDocument']['documentCreatedOn'], 'd.m.Y');
        $mapped['temps']['date'] = PrintoutHelper::dateConverter($mapped['Kalkulationsdatenblatt Datum'], 'd.m.Y');
        $mapped['temps']['angebots_nr'] = $mapped['Kalkulationsdatenblatt Angebots Nr.'];
        return array_merge($mapped, $displayInsideChildren);
    }

    public function getDisplayInside($document): array
    {
        if (!isset($document['children']))
            return [];
        $data = [];
        foreach ($document['children'] as $parent) {
            foreach ($document['children'] as $child) {
                if (isset($child['displayInside']) && $parent['id'] === $child['displayInside']) {
                    $title = $parent['title'] . ' ' . $child['title'];
                    $data[$title] = $child['reportedValue'];
                }
            }
        }

        return $data;
    }
}
