<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_BautagesberichtRustemeier
{
    public function getData($schemaId, $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], true);
        $data['address'] = $this->downloadAddressByd(1, $curl);
        $data['documentId'] = $documentId;
        return $data;
    }

    public function downloadAddressByd($id, $curl)
    {
        return json_decode($curl->_simple_call('get', "v1/addresses/$id", [],
            PrintoutHelper::getHeadersForApiCalls()), true);
    }
}