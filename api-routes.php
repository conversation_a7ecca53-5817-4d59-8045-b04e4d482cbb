<?php

enum TemplatePart: string
{
    case content = 'content';
    case header = 'header';
    case footer = 'footer';
}

enum RenderEngine
{
    case wkhtmltopdf;
    /**
     * Always prefer Playwright for newer printouts, except when JavaScript is required for headers/footers or when the
     * headers/footers need to be dynamic based on page.
     */
    case playwright;
}

enum RouteConfigParameterType
{
    case int;
    /**
     * "true", "false", "1", or "0"
     */
    case bool;
    case date;
    /**
     * This is a comma separated list of integers like 30,60,90.
     */
    case arrayInt;
    case ktrAanr;
    /**
     * This allows anything without validation.
     */
    case string;

    public function getSwaggerExample(): string
    {
        return match ($this) {
            RouteConfigParameterType::int => "144",
            RouteConfigParameterType::bool => "true",
            RouteConfigParameterType::date => "2024-12-31",
            RouteConfigParameterType::arrayInt => "30,60,90",
            RouteConfigParameterType::ktrAanr => "30-60",
            RouteConfigParameterType::string => "string",
        };
    }

    public function getName(): string|null
    {
        return match ($this) {
            RouteConfigParameterType::int => "integer",
            RouteConfigParameterType::bool => "boolean",
            RouteConfigParameterType::date => "date",
            RouteConfigParameterType::arrayInt => "comma separated integers",
            RouteConfigParameterType::ktrAanr => null,
            RouteConfigParameterType::string => "string",
        };
    }

    /**
     * Calls die() on any error.
     */
    public function validate(string $value, string $urlQueryName): void
    {
        switch ($this) {
            case RouteConfigParameterType::int:
                if (!preg_match('/^-?\d+$/', $value)) {
                    http_response_code(400);
                    die("Passed value ($value) for name ($urlQueryName) does not conform to int type. Allowed values are: " .
                        "-1, 0, 1 or any other integers");
                }
                break;
            case RouteConfigParameterType::bool:
                if (!($value === "true" || $value === "false" || $value === "1" || $value === "0")) {
                    http_response_code(400);
                    die("Passed value ($value) for name ($urlQueryName) does not conform to bool type. Allowed values are: " .
                        "true, false, 1, 0");
                }
                break;
            case RouteConfigParameterType::date:
                if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                    http_response_code(400);
                    die("Passed value ($value) for name ($urlQueryName) does not conform to date type. Allowed values are: " .
                        "2034-12-31 in yyyy-mm-dd format (year-month-day)");
                }
                break;
            case RouteConfigParameterType::arrayInt:
                if (!preg_match('/^(-?\d+)(,-?\d+)*$/', $value)) {
                    http_response_code(400);
                    die("Passed value ($value) for name ($urlQueryName) does not conform to array-int type. Allowed values are: " .
                        "-99,0,101 - integers joined together by commas or just a single integer like 60");
                }
                break;
            case RouteConfigParameterType::ktrAanr:
                if (!preg_match('/^\d+-\d+$/', $value)) {
                    http_response_code(400);
                    die("Passed value ($value) for name ($urlQueryName) does not conform to ktr-aanr type. Allowed values are: " .
                        "100-200 - 2 integers joined together via a minus, positive integers only");
                }
                break;
            case RouteConfigParameterType::string:
                // assume everything is valid
                break;
        }
    }
}

class PlaywrightConfig
{
    public int|null $marginTopMm = null;
    public int|null $marginBottomMm = null;
    public int|null $marginLeftMm = null;
    public int|null $marginRightMm = null;
    public string $format = "A4";
    public bool $isLandscape = false;

    public function __construct(
        int|null $marginTopMm = null,
        int|null $marginBottomMm = null,
        int|null $marginLeftMm = null,
        int|null $marginRightMm = null,
        string   $format = "A4",
        bool     $isLandscape = false
    )
    {
        $this->marginTopMm = $marginTopMm;
        $this->marginBottomMm = $marginBottomMm;
        $this->marginLeftMm = $marginLeftMm;
        $this->marginRightMm = $marginRightMm;
        $this->format = $format;
        $this->isLandscape = $isLandscape;
    }
}

enum PrintoutRequestMethod
{
    case GET;
    case POST;
}

/**
 * Every config.php in the api-routes/ subdirectories needs to return this instance.
 */
class RouteConfig
{
    /**
     * @var array set via {@link setParameters}
     */
    /**
     * @var array<int, RouteConfigParameter> set via {@link setParameters}
     */
    private array $parameters = [];
    /** @var array<string, mixed> $wkhtmltopdfArguments */
    public array $wkhtmltopdfArguments = [];
    public RenderEngine $renderEngine = RenderEngine::wkhtmltopdf;
    private PlaywrightConfig $playwrightConfig;

    /**
     * The request method for the route.
     */
    public PrintoutRequestMethod $requestMethod = PrintoutRequestMethod::GET;

    /**
     * @var array<RouteConfigParameter> list of routed URI segments
     */
    private array $routeSegments = [];

    /**
     * @var string the route name like "Baustellenplan" which needs to be in the proper directory name casing.
     */
    public string $redirectToRoute;

    /**
     * @var callable|null Will be executed before handling the request to generate the output.
     * Accept $urlParams as the first and only parameter passed by reference.
     */
    public $beforeRouteCallback = null;

    /**
     * @var callable|null Will be executed after generating the PDF, but before serving the PDF file and finalizing the request.
     * Accepts one string parameter, the full path to the generated PDF file.
     */
    public $afterPdfGenerationCallback = null;

    /**
     * Callback to resolve which subdirectory under api-routes/<route> holds your templates.
     *
     * Signature: fn(TemplatePart $part, string $apiRoute): string
     *
     * $part     - which template you’re rendering (content, header, footer)
     * $apiRoute - the base route name (i.e. the directory name)
     *
     * Return the directory name relative to api-routes/$apiRoute.
     * Defaults to just $apiRoute if no callback is set.
     *
     * @var callable|null
     */
    private $templateDirCallback = null;

    /**
     * @param callable(TemplatePart,string):string $cb
     */
    public function setTemplateDirCallback(callable $cb): void
    {
        $this->templateDirCallback = $cb;
    }

    /**
     * Resolve the directory under api-routes for the given part.
     */
    public function getTemplateDir(TemplatePart $part, string $apiRoute): string
    {
        if ($this->templateDirCallback !== null) {
            return call_user_func($this->templateDirCallback, $part, $apiRoute);
        }
        return $apiRoute;
    }

    public function getPlaywrightConfig(): PlaywrightConfig
    {
        return $this->playwrightConfig;
    }

    /**
     * Does nothing if $afterPdfGenerationCallback is not set.
     */
    public function callAfterPdfGenerationCallback(string $pdfPath): void
    {
        if ($this->afterPdfGenerationCallback != null) {
            call_user_func_array($this->afterPdfGenerationCallback, [$pdfPath]);
        }
    }

    public function usePlaywright(PlaywrightConfig $config = new PlaywrightConfig()): void
    {
        $this->playwrightConfig = $config;
        $this->renderEngine = RenderEngine::playwright;
    }

    public function getRenderEngine(): RenderEngine
    {
        return $this->renderEngine;
    }

    public function addRouteSegment(RouteConfigParameterType $type, string $name, bool $required): void
    {
        $this->routeSegments[] = new RouteConfigParameter($type, $name, $required);
    }

    /** @return array<RouteConfigParameter> */
    public function getRouteSegments(): array
    {
        return $this->routeSegments;
    }

    /**
     * Note that the order needs to match with the model's getData() function.
     */
    public function addParameter(RouteConfigParameterType $type, string $name, bool $required): void
    {
        $this->parameters[] = new RouteConfigParameter($type, $name, $required);
    }

    /**
     * @return array of {@link RouteConfigParameter}
     */
    /** @return array<RouteConfigParameter> */
    public function getParameters(): array
    {
        return $this->parameters;
    }

    /** @param array<string, string> $segments */
    public function validateRouteSegments(array $segments = []): void
    {
        $requiredSegments = array_filter($this->getRouteSegments(), fn($s) => $s->required);
        $segmentNames = array_map(fn($d) => $d->name, $requiredSegments);
        $diff = array_diff(array_values($segmentNames), array_keys($segments));

        if (!empty($diff)) {
            die_with_response_code(Response::BAD_REQUEST, "Missing parameters: " . implode($diff));
        }
    }

    /** @param array<string> $urlParamsNames */
    public function validateThatAllRequiredParametersAreSet(array $urlParamsNames, string $apiRoute): void
    {
        $requiredParameterNames = [];
        /**
         * @var RouteConfigParameter $param
         */
        foreach ($this->getParameters() as $param) {
            if ($param->required) {
                $requiredParameterNames[] = $param->name;
            }
        }
        foreach ($requiredParameterNames as $name) {
            if (!in_array($name, $urlParamsNames)) {
                http_response_code(400);
                die("The required parameter \"$name\" is missing. The required parameters to this API route " .
                    "($apiRoute) are: " . implode(", ", $requiredParameterNames));
            }
        }
    }

    public function validateUrlParamTypesVia_GET(): void
    {
        foreach ($_GET as $key => $value) {
            $foundType = null;
            $foundKey = null;
            $foundValue = null;
            /**
             * @var RouteConfigParameter $param
             */
            foreach ($this->getParameters() as $param) {
                if ($key === $param->name) {
                    $foundValue = $value;
                    $foundKey = $key;
                    $foundType = $param->type;
                    break;
                }
            }
            if ($foundKey === null && $key !== PrintoutHelper::AppendPdfsToPrintoutQueryParameter) {
                http_response_code(500);
                die("Did not find matching URL parameters (" . print_r($_GET, true) . ") by name in " .
                    print_r($this->parameters, true));
            }
            if ($key !== PrintoutHelper::AppendPdfsToPrintoutQueryParameter) {
                $foundType->validate($foundValue, $foundKey);
            }
        }
    }
}

class RouteConfigParameter
{
    public function __construct(
        public RouteConfigParameterType $type,
        public string                   $name,
        public bool                     $required
    )
    {
    }
}

/**
 * Return a mapping of lowercase API route to properly cased directory name.
 * This is required to allow for case-insensitive API routes.
 *
 * @return array with proper cased directory names as keys and their lower cased names as values
 */
/**
 * @return array<string, string>
 */
function getRouteDirectoryMapping(): array
{
    $mapping = [];
    $directoryIterator = new DirectoryIterator(__DIR__ . '/api-routes');
    foreach ($directoryIterator as $fileInfo) {
        if ($fileInfo->isDir() && !$fileInfo->isDot()) {
            $filename = $fileInfo->getFilename();
            $mapping[$filename] = strtolower($filename);
        }
    }
    return $mapping;
}

/**
 * @param array<string, string> $urlParams
 */
function saveGetDataResultAsTemporaryJsonFile(string $apiRoute, array $urlParams, string $basePathApiRoutes): void
{
    $uniqueTmpFilePath = generateUidFilePathWithTimestamp(".json");
    $_GET[PrintoutHelper::TempsQueryParameter] = $uniqueTmpFilePath;

    // Store entire result of getData() in a temporary file for access in the header/footer files.
    // Previously, the entire getData() result was passed via --post but this fails in wkhtmltopdf and the content ends up as a blank page.
    // The compilation order in wkhtmltopdf is always:
    //
    // - header/footer (not sure which one first)
    // - content
    //
    // The previous approach with the temps.json only works because the scaffoldingAPI code calls getData() internally first, exactly
    // as is done here:
    /** @noinspection PhpUndefinedFunctionInspection it is defined in index.php, but PhpStorm warns incorrectly unlike PHPStan */
    file_put_contents($uniqueTmpFilePath, json_encode(returnGetDataResult($basePathApiRoutes, $apiRoute, $urlParams)));
}

function executePhpFileAndReturnFilePathHtml(string $filePath): string
{
    ob_start();
    require_once $filePath;
    $output = ob_get_contents();
    ob_end_clean();
    $newFilePath = generateUidFilePathWithTimestamp(".html");
    file_put_contents($newFilePath, $output);
    return $newFilePath;
}

/**
 * @param array<string, string> $urlParams
 * @return string JSON encoded array
 */
function constructPlaywrightServerRequestBody(
    RouteConfig $config, string $outputPath, string $apiRoute, array $urlParams): string
{
    $requestBody = [];
    $playwrightConfig = $config->getPlaywrightConfig();

    if ($playwrightConfig->isLandscape) {
        $requestBody['landscape'] = true;
    }
    if ($playwrightConfig->format != "A4") {
        $requestBody["format"] = $playwrightConfig->format;
    }
    if ($playwrightConfig->marginTopMm != null) {
        $requestBody["marginTopMm"] = $playwrightConfig->marginTopMm;
    }
    if ($playwrightConfig->marginRightMm != null) {
        $requestBody["marginRightMm"] = $playwrightConfig->marginRightMm;
    }
    if ($playwrightConfig->marginBottomMm != null) {
        $requestBody["marginBottomMm"] = $playwrightConfig->marginBottomMm;
    }
    if ($playwrightConfig->marginLeftMm != null) {
        $requestBody["marginLeftMm"] = $playwrightConfig->marginLeftMm;
    }

    $contentDir = $config->getTemplateDir(TemplatePart::content, $apiRoute);
    saveGetDataResultAsTemporaryJsonFile($apiRoute, $urlParams, Request::$basePathApiRoutes);
    $bodyTpl = Request::$basePathApiRoutes . $contentDir . '/template.content.php';
    $requestBody['bodyFile'] = executePhpFileAndReturnFilePathHtml($bodyTpl);

    $headerDir = $config->getTemplateDir(TemplatePart::header, $apiRoute);
    $headerTpl = Request::$basePathApiRoutes . $headerDir . '/template.header.php';
    if (file_exists($headerTpl)) {
        $requestBody['headerFile'] = executePhpFileAndReturnFilePathHtml($headerTpl);
    }

    $footerDir = $config->getTemplateDir(TemplatePart::footer, $apiRoute);
    $footerTpl = Request::$basePathApiRoutes . $footerDir . '/template.footer.php';
    if (file_exists($footerTpl)) {
        $requestBody['footerFile'] = executePhpFileAndReturnFilePathHtml($footerTpl);
    }

    $requestBody['outputFile'] = $outputPath;
    return json_encode($requestBody);
}

/**
 * Dies on any generation errors.
 */
function callPlaywrightServerToGeneratePdf(string $requestBody): void
{
    if (defined('PLAYWRIGHT_SERVER_PORT')) {
        $serverPort = PLAYWRIGHT_SERVER_PORT;
    } else {
        $serverPort = 3000;
    }
    $url = 'http://localhost:' . $serverPort;
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $requestBody);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($requestBody)
    ]);
    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        die_with_response_code(message: 'Curl error on communicating with the local Playwright server: ' . curl_error($ch));
    } else {
        $httpStatusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($httpStatusCode != 200) {
            die_with_response_code(message: "Playwright PDF generation failed with status code $httpStatusCode. Response body: \n" . $response);
        }
    }
    curl_close($ch);
}

/**
 * Merges PDFs via the qpdf process.
 *
 * @param array<string> $pdfPaths
 * @param callable|null $afterPdfGenerationCallback Accepts one string parameter, the full path to the generated PDF file. Only called,
 *   when qpdf merged the PDFs successfully
 * @param string $outputPath should not be any of the input files, else qpdf fails
 * @return string the output PDF path. Can be the first element of $pdfPaths, when qpdf is not installed
 * @throws Exception when $pdfPaths contains less than 2 elements
 */
function mergePdfs(array $pdfPaths, string $outputPath, callable|null $afterPdfGenerationCallback = null): string
{
    if (count($pdfPaths) <= 1) {
        throw new Exception("Passed paths to PDF must be at least 2!");
    }
    $escapedInputPaths = implode(' ', array_map('escapeshellarg', $pdfPaths));
    $escapedOutputPath = escapeshellarg($outputPath);
    exec("qpdf --empty --pages $escapedInputPaths -- $escapedOutputPath 2>&1", $output, $code);
    if ($code == 127) {
        // qpdf not installed, in this case, just provide the PDF without merging - should not happen in the
        // Docker environment!
        return $pdfPaths[0];
    }
    if ($code == 0) {
        if ($afterPdfGenerationCallback) {
            call_user_func_array($afterPdfGenerationCallback, [$outputPath]);
        }
        return $outputPath;
    }

    $outputString = implode("\n", $output);
    die_with_response_code(Response::SERVER_ERROR, "qpdf exited with code $code: $outputString");
}